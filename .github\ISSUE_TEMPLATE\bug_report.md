---
name: Bug report
about: Create a report to help us improve AKlippy
title: '[BUG] '
labels: bug
assignees: ''

---

**Describe the bug**
A clear and concise description of what the bug is.

**To Reproduce**
Steps to reproduce the behavior:
1. Configuration used: '...'
2. Command executed: '...'
3. Hardware setup: '...'
4. See error

**Expected behavior**
A clear and concise description of what you expected to happen.

**Actual behavior**
What actually happened instead.

**Environment:**
 - OS: [e.g. Ubuntu 20.04]
 - AKlippy version: [e.g. v0.1.0]
 - Python version: [e.g. 3.9.2]
 - MCU type: [e.g. STM32F407]
 - Hardware: [e.g. development board, real engine]

**Configuration file**
```ini
# Paste your configuration file here (remove sensitive information)
```

**Log output**
```
# Paste relevant log output here
```

**Additional context**
Add any other context about the problem here.

**Safety Impact**
- [ ] This bug could affect engine safety
- [ ] This is a development/testing issue only
- [ ] Unsure about safety impact

**Checklist**
- [ ] I have searched existing issues for duplicates
- [ ] I have provided all requested information
- [ ] I have tested with the latest version
- [ ] I have validated my configuration file
