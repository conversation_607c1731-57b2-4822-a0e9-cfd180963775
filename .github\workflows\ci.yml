name: <PERSON><PERSON><PERSON> CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: [3.8, 3.9, "3.10", "3.11"]

    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
    
    - name: Install system dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y gcc-arm-none-eabi
    
    - name: Cache pip dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements*.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-
    
    - name: Install Python dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install -r requirements-dev.txt
    
    - name: Lint with flake8
      run: |
        flake8 aklippy/ tools/ --count --select=E9,F63,F7,F82 --show-source --statistics
        flake8 aklippy/ tools/ --count --exit-zero --max-complexity=10 --max-line-length=88 --statistics
    
    - name: Format check with black
      run: |
        black --check aklippy/ tools/
    
    - name: Type check with mypy
      run: |
        mypy aklippy/ --ignore-missing-imports
    
    - name: Test with pytest
      run: |
        pytest tests/unit/ -v --cov=aklippy --cov-report=xml
    
    - name: Validate example configurations
      run: |
        python tools/config_validator.py configs/example_basic.cfg
        python tools/config_validator.py docs/examples/4_cylinder_turbo.cfg
        python tools/config_validator.py docs/examples/v8_naturally_aspirated.cfg

  build-firmware:
    runs-on: ubuntu-latest
    needs: test
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Install ARM toolchain
      run: |
        sudo apt-get update
        sudo apt-get install -y gcc-arm-none-eabi
    
    - name: Build STM32F4 firmware
      run: |
        cd amcu
        make TARGET=stm32f4
    
    - name: Build STM32F7 firmware
      run: |
        cd amcu
        make clean TARGET=stm32f4
        make TARGET=stm32f7
    
    - name: Upload firmware artifacts
      uses: actions/upload-artifact@v3
      with:
        name: firmware-binaries
        path: amcu/out/*.bin

  security-scan:
    runs-on: ubuntu-latest
    needs: test
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Run Bandit security scan
      run: |
        pip install bandit
        bandit -r aklippy/ -f json -o bandit-report.json
    
    - name: Upload security scan results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: security-scan
        path: bandit-report.json

  documentation:
    runs-on: ubuntu-latest
    needs: test
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: 3.9
    
    - name: Install documentation dependencies
      run: |
        pip install sphinx sphinx-rtd-theme myst-parser
    
    - name: Build documentation
      run: |
        cd docs
        make html
    
    - name: Upload documentation
      uses: actions/upload-artifact@v3
      with:
        name: documentation
        path: docs/_build/html/
