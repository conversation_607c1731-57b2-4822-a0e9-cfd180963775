# AKlippy .gitignore

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
aklippy-env/
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/
/var/log/aklippy/

# Configuration (user-specific)
my_config.cfg
local_config.cfg
*.local.cfg

# Build artifacts
amcu/build/
amcu/out/
*.elf
*.hex
*.bin
*.map
*.dis

# Temporary files
*.tmp
*.temp
*.bak
*.backup

# Test results
.pytest_cache/
.coverage
htmlcov/
.tox/

# Documentation build
docs/_build/
docs/.doctrees/

# Firmware development
*.o
*.d
*.lst

# Hardware-specific
*.pcb
*.sch#
*.brd#

# Secrets and keys
*.key
*.pem
secrets.txt
.env

# User data
user_data/
calibration_data/
log_data/
