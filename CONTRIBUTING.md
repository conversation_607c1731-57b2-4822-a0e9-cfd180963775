# Contributing to <PERSON><PERSON><PERSON>

Thank you for your interest in contributing to <PERSON><PERSON><PERSON>! This document provides guidelines for contributing to the project.

## Code of Conduct

By participating in this project, you agree to abide by our Code of Conduct:
- Be respectful and inclusive
- Focus on constructive feedback
- Help create a welcoming environment for all contributors
- Remember that safety is paramount in automotive applications

## How to Contribute

### Reporting Issues

Before creating an issue, please:
1. Search existing issues to avoid duplicates
2. Use the appropriate issue template
3. Provide detailed information including:
   - AKlippy version
   - Hardware configuration
   - Steps to reproduce
   - Expected vs actual behavior
   - Log files (if applicable)

### Submitting Code Changes

1. **Fork the repository**
2. **Create a feature branch**
   ```bash
   git checkout -b feature/your-feature-name
   ```
3. **Make your changes**
   - Follow the coding standards below
   - Add tests for new functionality
   - Update documentation as needed
4. **Test thoroughly**
   - Run all existing tests
   - Test on actual hardware if possible
   - Validate configuration changes
5. **Commit your changes**
   ```bash
   git commit -m "Add feature: brief description"
   ```
6. **Push to your fork**
   ```bash
   git push origin feature/your-feature-name
   ```
7. **Create a Pull Request**

### Pull Request Guidelines

- Use a clear, descriptive title
- Reference any related issues
- Provide a detailed description of changes
- Include test results and validation
- Ensure all CI checks pass
- Be responsive to review feedback

## Development Setup

### Prerequisites

- Python 3.8+
- ARM GCC toolchain
- Git
- Virtual environment tools

### Setup Instructions

1. **Clone the repository**
   ```bash
   git clone https://github.com/your-org/aklippy.git
   cd aklippy
   ```

2. **Create virtual environment**
   ```bash
   python3 -m venv aklippy-env
   source aklippy-env/bin/activate  # Linux/Mac
   # or
   aklippy-env\Scripts\activate     # Windows
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   pip install -r requirements-dev.txt  # Development dependencies
   ```

4. **Install pre-commit hooks**
   ```bash
   pre-commit install
   ```

## Coding Standards

### Python Code

- Follow PEP 8 style guidelines
- Use type hints where appropriate
- Write docstrings for all public functions/classes
- Maximum line length: 88 characters (Black formatter)
- Use meaningful variable and function names

Example:
```python
def calculate_fuel_pulse_width(
    load_percent: float, 
    rpm: int, 
    temperature: float
) -> float:
    """
    Calculate fuel injection pulse width.
    
    Args:
        load_percent: Engine load percentage (0-100)
        rpm: Engine RPM
        temperature: Coolant temperature in Celsius
        
    Returns:
        Pulse width in milliseconds
    """
    # Implementation here
    pass
```

### C Code (MCU Firmware)

- Follow Linux kernel coding style
- Use descriptive function and variable names
- Add comprehensive comments
- Maximum line length: 80 characters
- Use consistent indentation (4 spaces)

Example:
```c
/**
 * @brief Calculate fuel injection timing
 * @param rpm Engine RPM
 * @param load Engine load percentage
 * @return Injection timing in microseconds
 */
uint32_t calculate_injection_timing(uint32_t rpm, uint8_t load)
{
    // Implementation here
    return timing_us;
}
```

### Configuration Files

- Use clear, descriptive option names
- Include comments explaining complex settings
- Provide reasonable defaults
- Validate all user inputs

## Testing Guidelines

### Unit Tests

- Write tests for all new functionality
- Aim for >90% code coverage
- Use pytest for Python tests
- Mock hardware interfaces for testing

### Integration Tests

- Test complete workflows
- Validate configuration parsing
- Test MCU communication protocols
- Verify safety mechanisms

### Hardware Testing

- Test on actual MCU hardware when possible
- Validate timing accuracy with oscilloscope
- Test all sensor inputs and actuator outputs
- Verify safety cutoffs and emergency stops

## Documentation

### Code Documentation

- Write clear docstrings for all public APIs
- Include usage examples
- Document configuration options
- Explain complex algorithms

### User Documentation

- Update relevant documentation for changes
- Include configuration examples
- Provide troubleshooting information
- Keep getting started guide current

## Safety Considerations

⚠️ **CRITICAL: Automotive applications involve safety risks**

When contributing:
- Always consider safety implications
- Test thoroughly before submitting
- Document any safety-related changes
- Follow automotive safety standards
- Never compromise on safety for features

### Safety Review Process

All changes affecting safety-critical functions require:
1. Detailed safety analysis
2. Multiple reviewer approval
3. Hardware validation
4. Documentation updates

## Release Process

### Version Numbering

We use Semantic Versioning (SemVer):
- MAJOR.MINOR.PATCH
- MAJOR: Breaking changes
- MINOR: New features (backward compatible)
- PATCH: Bug fixes

### Release Checklist

- [ ] All tests pass
- [ ] Documentation updated
- [ ] Version numbers updated
- [ ] Changelog updated
- [ ] Hardware validation complete
- [ ] Security review complete

## Community

### Communication Channels

- **GitHub Issues**: Bug reports and feature requests
- **GitHub Discussions**: General questions and ideas
- **Discord**: Real-time chat (link in README)
- **Forum**: Long-form discussions

### Getting Help

- Check the documentation first
- Search existing issues and discussions
- Ask questions in the appropriate channel
- Be patient and respectful

## Recognition

Contributors will be recognized in:
- CONTRIBUTORS.md file
- Release notes
- Project documentation
- Annual contributor highlights

## Legal

By contributing to AKlippy, you agree that:
- Your contributions will be licensed under GPL v3
- You have the right to submit the contribution
- You understand the automotive safety implications
- You accept the project's disclaimer of warranty

Thank you for helping make AKlippy better and safer for everyone!
