# AKlippy - Automotive Klipper ECU Framework

[![License: GPL v3](https://img.shields.io/badge/License-GPLv3-blue.svg)](https://www.gnu.org/licenses/gpl-3.0)
[![Build Status](https://img.shields.io/badge/build-passing-brightgreen.svg)]()

## Overview

AKlippy (Automotive Klipper) is an open-source ECU firmware framework inspired by the Klipper 3D printer firmware architecture. It provides a distributed system that separates high-level control logic running on a host computer from time-critical, low-level I/O operations running on automotive-grade microcontrollers.

## Key Features

- **Distributed Architecture**: Separates complex algorithms from real-time operations
- **Real-time Precision**: Critical timing operations handled directly by MCU
- **Hardware Abstraction**: Support for multiple MCU platforms and automotive hardware
- **Modular Design**: Extensible framework for various automotive control functions
- **Open Source**: Fully open-source with community-driven development

## Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    AKlippy Host Software                    │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │ Engine Control  │  │   Diagnostics   │  │ User Interface│ │
│  │    Modules      │  │   & Logging     │  │  & Tuning   │ │
│  └─────────────────┘  └─────────────────┘  └─────────────┘ │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │           Communication Protocol Layer                  │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
                    ┌───────────┴───────────┐
                    │  Binary Protocol      │
                    │  (CAN/Serial/USB)     │
                    └───────────┬───────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                   AMCU Firmware (MCU)                      │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │   Real-time     │  │  Sensor Input   │  │  Actuator   │ │
│  │   I/O Control   │  │   Processing    │  │   Drivers   │ │
│  └─────────────────┘  └─────────────────┘  └─────────────┘ │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              Hardware Abstraction Layer                │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## Project Structure

```
aklippy/
├── docs/                    # Documentation
├── aklippy/                 # Host software (Python)
│   ├── core/               # Core framework components
│   ├── modules/            # Automotive control modules
│   ├── protocol/           # Communication protocol
│   └── config/             # Configuration system
├── amcu/                   # MCU firmware (C)
│   ├── src/                # Source code
│   ├── hal/                # Hardware abstraction layer
│   └── drivers/            # Hardware drivers
├── tools/                  # Build and development tools
├── configs/                # Example configurations
├── tests/                  # Test suites
└── scripts/                # Utility scripts
```

## Getting Started

### Prerequisites

- Python 3.8+
- ARM GCC toolchain for MCU compilation
- Supported MCU development board
- CAN interface (optional)

### Installation

```bash
git clone https://github.com/your-org/aklippy.git
cd aklippy
pip install -r requirements.txt
```

### Quick Start

1. **Configure your hardware**: Copy and modify a configuration from `configs/`
2. **Build MCU firmware**: Use the build tools to compile for your target MCU
3. **Flash firmware**: Upload the compiled firmware to your MCU
4. **Start AKlippy host**: Run the host software with your configuration

## Supported Hardware

### MCU Platforms
- STM32F4xx series
- STM32F7xx series
- ESP32 (planned)
- Teensy 4.x (planned)

### Automotive Interfaces
- CAN bus (ISO 11898)
- LIN bus (ISO 17987)
- Analog sensors (temperature, pressure, position)
- Digital sensors (hall effect, optical)
- PWM actuators (injectors, ignition, throttle)

## Control Modules

- **Fuel Injection**: Sequential and batch injection control
- **Ignition Timing**: Precise spark timing control
- **Idle Air Control**: Electronic throttle and IAC valve control
- **Closed-Loop Control**: Lambda/O2 sensor feedback
- **Variable Valve Timing**: VVT solenoid control
- **Boost Control**: Wastegate and blow-off valve control
- **Launch Control**: Rev limiting and traction control
- **Diagnostics**: OBD-II and custom diagnostic routines

## Configuration

AKlippy uses human-readable configuration files similar to Klipper's approach:

```ini
[mcu]
serial: /dev/ttyUSB0
baud: 250000

[engine]
cylinders: 4
displacement: 2000
compression_ratio: 10.5

[fuel_injection]
injector_flow_rate: 440
fuel_pressure: 3.0

[ignition]
coil_charge_time: 3.0
dwell_time: 2.5
```

## Contributing

We welcome contributions! Please see [CONTRIBUTING.md](CONTRIBUTING.md) for guidelines.

## License

This project is licensed under the GNU General Public License v3.0 - see the [LICENSE](LICENSE) file for details.

## Acknowledgments

- Inspired by the [Klipper](https://github.com/Klipper3d/klipper) 3D printer firmware
- Built upon research from open-source ECU projects like [rusEFI](https://rusefi.com/) and [Speeduino](https://speeduino.com/)
- Thanks to the automotive open-source community

## Disclaimer

This software is intended for educational and research purposes. Use in production vehicles requires thorough testing and validation. The authors are not responsible for any damage or safety issues resulting from the use of this software.
