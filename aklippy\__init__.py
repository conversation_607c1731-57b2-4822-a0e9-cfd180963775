"""
AKlippy - Automotive Klipper ECU Framework

This package provides the host software for the AKlippy distributed ECU system.
"""

__version__ = "0.1.0"
__author__ = "AKlippy Development Team"
__license__ = "GPL-3.0"

from .core.reactor import Reactor
from .core.aklippy import <PERSON><PERSON>py
from .protocol.serialhdl import Serial<PERSON>eader
from .config.configfile import ConfigFile

__all__ = [
    'Reactor',
    'AKlippy', 
    'SerialReader',
    'ConfigFile'
]
