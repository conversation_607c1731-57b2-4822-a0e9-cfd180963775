"""
Configuration File Parser

Handles parsing and validation of AKlippy configuration files.
"""

import configparser
import logging
import os
import re

class ConfigWrapper:
    """Wrapper for configuration sections with validation"""
    
    def __init__(self, printer, fileconfig, section_name):
        self.printer = printer
        self.fileconfig = fileconfig
        self.section_name = section_name
        self.section = fileconfig[section_name] if section_name in fileconfig else {}
        self.accessed = set()
        
    def get_name(self):
        """Get section name"""
        return self.section_name
        
    def get_printer(self):
        """Get printer instance"""
        return self.printer
        
    def get(self, option, default=None, note_valid=True):
        """Get a string configuration value"""
        if note_valid:
            self.accessed.add(option)
        return self.section.get(option, default)
        
    def getint(self, option, default=None, minval=None, maxval=None):
        """Get an integer configuration value"""
        self.accessed.add(option)
        value = self.section.get(option)
        if value is None:
            if default is not None:
                return default
            raise self.error("Option '%s' not found" % option)
            
        try:
            result = int(value)
        except ValueError:
            raise self.error("Option '%s' is not a valid integer: %s" % (option, value))
            
        if minval is not None and result < minval:
            raise self.error("Option '%s' value %d is below minimum %d" % 
                           (option, result, minval))
        if maxval is not None and result > maxval:
            raise self.error("Option '%s' value %d is above maximum %d" % 
                           (option, result, maxval))
                           
        return result
        
    def getfloat(self, option, default=None, minval=None, maxval=None):
        """Get a float configuration value"""
        self.accessed.add(option)
        value = self.section.get(option)
        if value is None:
            if default is not None:
                return default
            raise self.error("Option '%s' not found" % option)
            
        try:
            result = float(value)
        except ValueError:
            raise self.error("Option '%s' is not a valid float: %s" % (option, value))
            
        if minval is not None and result < minval:
            raise self.error("Option '%s' value %f is below minimum %f" % 
                           (option, result, minval))
        if maxval is not None and result > maxval:
            raise self.error("Option '%s' value %f is above maximum %f" % 
                           (option, result, maxval))
                           
        return result
        
    def getboolean(self, option, default=None):
        """Get a boolean configuration value"""
        self.accessed.add(option)
        value = self.section.get(option)
        if value is None:
            if default is not None:
                return default
            raise self.error("Option '%s' not found" % option)
            
        value = value.lower()
        if value in ('true', 'yes', '1', 'on'):
            return True
        elif value in ('false', 'no', '0', 'off'):
            return False
        else:
            raise self.error("Option '%s' is not a valid boolean: %s" % (option, value))
            
    def getchoice(self, option, choices, default=None):
        """Get a choice configuration value"""
        value = self.get(option, default)
        if value not in choices:
            raise self.error("Option '%s' value '%s' is not one of: %s" % 
                           (option, value, ', '.join(choices)))
        return value
        
    def getlist(self, option, default=None, separator=','):
        """Get a list configuration value"""
        value = self.get(option, default)
        if value is None:
            return []
        return [item.strip() for item in value.split(separator) if item.strip()]
        
    def getintlist(self, option, default=None, separator=','):
        """Get a list of integers"""
        str_list = self.getlist(option, default, separator)
        try:
            return [int(item) for item in str_list]
        except ValueError as e:
            raise self.error("Option '%s' contains invalid integer: %s" % (option, str(e)))
            
    def getfloatlist(self, option, default=None, separator=','):
        """Get a list of floats"""
        str_list = self.getlist(option, default, separator)
        try:
            return [float(item) for item in str_list]
        except ValueError as e:
            raise self.error("Option '%s' contains invalid float: %s" % (option, str(e)))
            
    def has_option(self, option):
        """Check if option exists"""
        return option in self.section
        
    def get_options(self):
        """Get all option names"""
        return list(self.section.keys())
        
    def error(self, msg):
        """Raise a configuration error"""
        raise Exception("Config error in section '%s': %s" % (self.section_name, msg))

class ConfigFile:
    """
    Configuration file parser and manager.
    
    Handles reading, parsing, and validating AKlippy configuration files.
    """
    
    def __init__(self, printer, filename):
        self.printer = printer
        self.filename = filename
        self.logger = logging.getLogger('config')
        
        # Configuration data
        self.fileconfig = configparser.ConfigParser()
        self.fileconfig.optionxform = str  # Preserve case
        
        # Tracking
        self.sections_accessed = set()
        self.autosave_options = {}
        
    def read_config(self, filename):
        """Read configuration from file"""
        if not os.path.exists(filename):
            raise Exception("Config file not found: %s" % filename)
            
        try:
            self.fileconfig.read(filename)
            self.logger.info("Configuration loaded from: %s", filename)
        except Exception as e:
            raise Exception("Error reading config file %s: %s" % (filename, str(e)))
            
    def read_main_config(self):
        """Read the main configuration file"""
        self.read_config(self.filename)
        return self
        
    def get_section_list(self):
        """Get list of all configuration sections"""
        return list(self.fileconfig.sections())
        
    def has_section(self, section_name):
        """Check if section exists"""
        return section_name in self.fileconfig
        
    def getsection(self, section_name):
        """Get a configuration section wrapper"""
        self.sections_accessed.add(section_name)
        return ConfigWrapper(self.printer, self.fileconfig, section_name)
        
    def check_unused_options(self, config):
        """Check for unused configuration options"""
        # This would check for options that were defined but never accessed
        # Useful for catching typos in configuration files
        pass
        
    def save_config_file(self, filename=None):
        """Save configuration to file"""
        if filename is None:
            filename = self.filename
            
        try:
            with open(filename, 'w') as f:
                self.fileconfig.write(f)
            self.logger.info("Configuration saved to: %s", filename)
        except Exception as e:
            raise Exception("Error saving config file %s: %s" % (filename, str(e)))
            
    def set_autosave(self, section_name, option, value):
        """Set an option for autosave"""
        if section_name not in self.autosave_options:
            self.autosave_options[section_name] = {}
        self.autosave_options[section_name][option] = str(value)
        
        # Update in-memory config
        if section_name not in self.fileconfig:
            self.fileconfig.add_section(section_name)
        self.fileconfig.set(section_name, option, str(value))
        
    def remove_section(self, section_name):
        """Remove a configuration section"""
        if section_name in self.fileconfig:
            self.fileconfig.remove_section(section_name)
            
    def get_status(self, eventtime):
        """Get configuration status"""
        return {
            'config_file': self.filename,
            'sections_count': len(self.fileconfig.sections()),
            'sections_accessed': len(self.sections_accessed)
        }
