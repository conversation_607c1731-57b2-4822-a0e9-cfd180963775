"""
AKlippy - Main application class

This is the main AKlippy application that coordinates all components.
"""

import logging
import os
import sys
import time
import traceback
import importlib

from .reactor import Reactor
from .mcu import MCU
from ..config.configfile import ConfigFile
from ..protocol.serialhdl import SerialReader

class AKlippy:
    """
    Main AKlippy application class.
    
    Coordinates the reactor, configuration, MCU communication,
    and automotive control modules.
    """
    
    def __init__(self, input_fd, output_fd, dictionary, start_args):
        self.printer_state_message = None
        self.in_shutdown_state = False
        self.run_result = None
        
        # Core components
        self.reactor = Reactor()
        self.objects = {}
        self.load_objects = {}
        self.lookup_objects = {}
        
        # Configuration
        self.start_args = start_args
        self.config_file = start_args.get('config_file')
        self.log_file = start_args.get('log_file')
        
        # Logging setup
        self._setup_logging()
        self.logger = logging.getLogger('aklippy')
        
        # Stats and monitoring
        self.stats_timer = None
        self.connect_timer = None
        
        # MCU communication
        self.mcu = None
        
        self.logger.info("Starting AKlippy v0.1.0")
        
    def _setup_logging(self):
        """Setup logging configuration"""
        log_level = logging.INFO
        if self.start_args.get('verbose'):
            log_level = logging.DEBUG
            
        # Configure root logger
        logging.basicConfig(
            level=log_level,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        # Add file handler if specified
        if self.log_file:
            file_handler = logging.FileHandler(self.log_file)
            file_handler.setLevel(log_level)
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            file_handler.setFormatter(formatter)
            logging.getLogger().addHandler(file_handler)
            
    def add_object(self, name, obj):
        """Add an object to the printer object registry"""
        if name in self.objects:
            raise self.config_error(
                "Printer object '%s' already exists" % (name,))
        self.objects[name] = obj
        
    def lookup_object(self, name, default=None):
        """Lookup an object in the registry"""
        if name in self.objects:
            return self.objects[name]
        if name in self.load_objects:
            self.load_objects[name].load_config(self.config)
        return self.objects.get(name, default)
        
    def lookup_objects(self, module=None):
        """Lookup multiple objects by module name"""
        if module is None:
            return list(self.objects.items())
        prefix = module + ' '
        objs = [(n, self.objects[n])
                for n in self.objects if n.startswith(prefix)]
        if module in self.objects:
            return [(module, self.objects[module])] + objs
        return objs
        
    def set_rollover_info(self, name, info, log=True):
        """Set rollover information for debugging"""
        if log:
            self.logger.info(info)
            
    def try_load_module(self, config, section):
        """Try to load a configuration module"""
        if section in self.objects:
            return self.objects[section]
        module_parts = section.split()
        module_name = module_parts[0]
        py_name = os.path.join(os.path.dirname(__file__), 
                              '..', 'modules', module_name + '.py')
        py_dirname = os.path.join(os.path.dirname(__file__), 
                                 '..', 'modules', module_name, '__init__.py')
        if os.path.exists(py_name):
            mod = importlib.import_module('aklippy.modules.' + module_name)
        elif os.path.exists(py_dirname):
            mod = importlib.import_module('aklippy.modules.' + module_name)
        else:
            raise config.error("Unable to load module '%s'" % (section,))
        init_func = 'load_config'
        if len(module_parts) > 1:
            init_func = 'load_config_prefix'
        init_func = getattr(mod, init_func, None)
        if init_func is None:
            if len(module_parts) == 1:
                raise config.error("Module '%s' is not configurable" % (section,))
            raise config.error(
                "Module '%s' is not multi-instance configurable" % (section,))
        self.objects[section] = init_func(config)
        return self.objects[section]
        
    def _read_config(self):
        """Read and parse configuration file"""
        self.objects['configfile'] = pconfig = ConfigFile(self, self.config_file)
        config = pconfig.read_main_config()
        
        # MCU configuration
        if config.has_section('mcu'):
            self.add_object('mcu', MCU(config.getsection('mcu'), self.reactor))
            self.mcu = self.objects['mcu']
        else:
            raise config.error("No [mcu] section found in config")
            
        # Load other modules
        for section_name in config.get_section_list():
            if section_name == 'mcu':
                continue
            self.try_load_module(config, section_name)
            
        # Validate configuration
        pconfig.check_unused_options(config)
        
    def _connect(self, eventtime):
        """Connect to MCU and initialize"""
        try:
            if self.mcu is not None:
                self.mcu.connect()
                self.logger.info("MCU connected successfully")
                
            # Initialize all objects
            for name, obj in self.objects.items():
                if hasattr(obj, 'connect'):
                    obj.connect()
                    
            self.printer_state_message = "Ready"
            self.logger.info("AKlippy ready for operation")
            
        except Exception as e:
            self.logger.exception("Connection error: %s", e)
            self.invoke_shutdown("Connection failed: %s" % str(e))
            
    def run(self):
        """Main application run loop"""
        try:
            # Read configuration
            self._read_config()
            
            # Schedule connection
            self.reactor.register_callback(self._connect)
            
            # Setup periodic stats
            self.stats_timer = self.reactor.register_timer(
                self._stats_callback, self.reactor.NOW)
                
            # Run reactor
            self.reactor.run()
            
        except Exception as e:
            self.logger.exception("AKlippy error: %s", e)
            self.invoke_shutdown("AKlippy error: %s" % str(e))
            
        return self.run_result
        
    def _stats_callback(self, eventtime):
        """Periodic stats callback"""
        # Update stats every 5 seconds
        self.reactor.update_timer(self.stats_timer, eventtime + 5.0)
        
        # Log basic stats
        if self.mcu:
            stats = self.mcu.stats(eventtime)
            self.logger.debug("MCU stats: %s", stats)
            
    def invoke_shutdown(self, msg):
        """Invoke system shutdown"""
        if self.in_shutdown_state:
            return
        self.logger.error("Invoking shutdown: %s", msg)
        self.in_shutdown_state = True
        self.printer_state_message = "Shutdown: %s" % msg
        
        # Shutdown all objects
        for obj in self.objects.values():
            if hasattr(obj, 'shutdown'):
                try:
                    obj.shutdown()
                except Exception as e:
                    self.logger.exception("Shutdown error: %s", e)
                    
        # Request reactor shutdown
        self.reactor.request_exit("Shutdown requested")
        
    def invoke_async_shutdown(self, msg):
        """Invoke shutdown from async context"""
        self.reactor.register_async_callback(
            lambda et: self.invoke_shutdown(msg))
            
    def is_shutdown(self):
        """Check if system is in shutdown state"""
        return self.in_shutdown_state
        
    def get_state_message(self):
        """Get current state message"""
        if self.printer_state_message is None:
            return "Startup"
        return self.printer_state_message
        
    def config_error(self, msg):
        """Raise a configuration error"""
        raise Exception("Config error: %s" % msg)
