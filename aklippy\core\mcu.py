"""
MCU - Microcontroller communication interface

Handles communication with the automotive MCU (AMCU) firmware.
"""

import logging
import time
import threading
from ..protocol.serialhdl import SerialReader

class MCUCommand:
    """Represents a command that can be sent to the MCU"""
    def __init__(self, mcu, msgformat, cmd_queue=None):
        self.mcu = mcu
        self.msgformat = msgformat
        self.cmd_queue = cmd_queue
        self.response = None
        
    def send(self, data=[], minclock=0, reqclock=0):
        """Send command to MCU"""
        return self.mcu.send(self, data, minclock, reqclock)

class MCUResponse:
    """Handles responses from the MCU"""
    def __init__(self, mcu, msgformat, callback):
        self.mcu = mcu
        self.msgformat = msgformat
        self.callback = callback

class MCU:
    """
    Microcontroller communication interface.
    
    Manages the connection to the automotive MCU and provides
    high-level interfaces for sending commands and receiving responses.
    """
    
    def __init__(self, config, reactor):
        self.reactor = reactor
        self.logger = logging.getLogger('mcu')
        
        # Configuration
        self.serial_port = config.get('serial', '/dev/ttyUSB0')
        self.baud_rate = config.getint('baud', 250000)
        self.mcu_type = config.get('mcu_type', 'stm32f4')
        
        # Connection state
        self.serial = None
        self.is_connected = False
        self.is_shutdown = False
        self.shutdown_msg = ""
        
        # Protocol state
        self.data_dictionary = {}
        self.commands = {}
        self.responses = {}
        self.pending_commands = {}
        self.command_queue = []
        
        # Statistics
        self.stats_sumsq = 0.
        self.stats_sum = 0.
        self.stats_count = 0
        self.last_stats_time = 0.
        
        # Threading
        self.lock = threading.Lock()
        
        self.logger.info("MCU configured: %s at %d baud", 
                        self.serial_port, self.baud_rate)
        
    def connect(self):
        """Connect to the MCU"""
        if self.is_connected:
            return
            
        self.logger.info("Connecting to MCU...")
        
        try:
            # Open serial connection
            self.serial = SerialReader(self.reactor, self.serial_port, self.baud_rate)
            self.serial.register_response(self._handle_response)
            
            # Get MCU identification
            self._identify_mcu()
            
            # Configure MCU
            self._configure_mcu()
            
            self.is_connected = True
            self.logger.info("MCU connected successfully")
            
        except Exception as e:
            self.logger.error("MCU connection failed: %s", e)
            self.shutdown("Connection failed")
            raise
            
    def _identify_mcu(self):
        """Identify MCU and get data dictionary"""
        # Send identify command
        identify_cmd = self.lookup_command("identify")
        
        # Get data dictionary in chunks
        offset = 0
        data_dict_parts = []
        
        while True:
            response = identify_cmd.send([offset, 40])
            if not response or len(response) < 40:
                break
            data_dict_parts.append(response)
            offset += len(response)
            
        # Parse data dictionary
        if data_dict_parts:
            import json
            import zlib
            dict_data = b''.join(data_dict_parts)
            dict_json = zlib.decompress(dict_data).decode('utf-8')
            self.data_dictionary = json.loads(dict_json)
            self.logger.info("MCU data dictionary loaded: %d commands, %d responses",
                           len(self.data_dictionary.get('commands', {})),
                           len(self.data_dictionary.get('responses', {})))
        else:
            raise Exception("Failed to get MCU data dictionary")
            
    def _configure_mcu(self):
        """Configure MCU for operation"""
        # Get MCU configuration
        config_cmd = self.lookup_command("get_config")
        config_response = config_cmd.send()
        
        # Set up clock synchronization
        self._sync_clock()
        
        self.logger.info("MCU configuration complete")
        
    def _sync_clock(self):
        """Synchronize clocks between host and MCU"""
        clock_cmd = self.lookup_command("get_clock")
        
        # Get initial clock reading
        start_time = time.time()
        clock_response = clock_cmd.send()
        end_time = time.time()
        
        if clock_response:
            mcu_clock = clock_response[0]
            host_time = (start_time + end_time) / 2.0
            self.logger.debug("Clock sync: MCU=%u, Host=%.6f", mcu_clock, host_time)
            
    def lookup_command(self, msgformat):
        """Look up a command by message format"""
        if msgformat in self.commands:
            return self.commands[msgformat]
            
        # Create new command
        cmd = MCUCommand(self, msgformat)
        self.commands[msgformat] = cmd
        return cmd
        
    def register_response(self, msgformat, callback):
        """Register a response handler"""
        response = MCUResponse(self, msgformat, callback)
        self.responses[msgformat] = response
        return response
        
    def send(self, cmd, data, minclock=0, reqclock=0):
        """Send a command to the MCU"""
        if self.is_shutdown:
            raise Exception("MCU is shutdown")
            
        with self.lock:
            # Add to command queue
            cmd_data = {
                'cmd': cmd,
                'data': data,
                'minclock': minclock,
                'reqclock': reqclock,
                'response': None
            }
            self.command_queue.append(cmd_data)
            
            # Send via serial
            if self.serial:
                self.serial.send_command(cmd.msgformat, data)
                
        return cmd_data.get('response')
        
    def _handle_response(self, params):
        """Handle response from MCU"""
        # Process response based on message format
        # This would be implemented based on the protocol specification
        pass
        
    def stats(self, eventtime):
        """Get MCU statistics"""
        with self.lock:
            if self.stats_count == 0:
                return "mcu: No stats available"
                
            avg = self.stats_sum / self.stats_count
            stddev = (self.stats_sumsq / self.stats_count - avg**2) ** 0.5
            
            stats_str = "mcu: avg=%.6f stddev=%.6f count=%d" % (
                avg, stddev, self.stats_count)
                
            # Reset stats
            self.stats_sum = self.stats_sumsq = self.stats_count = 0
            self.last_stats_time = eventtime
            
            return stats_str
            
    def shutdown(self, msg=""):
        """Shutdown MCU connection"""
        if self.is_shutdown:
            return
            
        self.logger.info("MCU shutdown: %s", msg)
        self.is_shutdown = True
        self.shutdown_msg = msg
        
        if self.serial:
            self.serial.disconnect()
            
    def is_fileoutput(self):
        """Check if output is to a file"""
        return False
        
    def get_status(self, eventtime):
        """Get MCU status"""
        return {
            'mcu_connected': self.is_connected,
            'mcu_shutdown': self.is_shutdown,
            'mcu_type': self.mcu_type,
            'serial_port': self.serial_port,
            'baud_rate': self.baud_rate
        }
