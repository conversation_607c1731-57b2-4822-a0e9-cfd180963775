"""
Reactor - Event-driven main loop for AKlippy

Based on <PERSON><PERSON><PERSON>'s reactor design, adapted for automotive applications.
"""

import select
import time
import heapq
import threading
import logging
import os

class ReactorTimer:
    """Timer object for scheduling callbacks"""
    def __init__(self, callback, waketime):
        self.callback = callback
        self.waketime = waketime
        
    def __lt__(self, other):
        return self.waketime < other.waketime

class ReactorFileHandler:
    """File descriptor handler for I/O operations"""
    def __init__(self, fd, callback):
        self.fd = fd
        self.callback = callback

class ReactorMutex:
    """Mutex for thread synchronization"""
    def __init__(self, reactor, is_locked=False):
        self.reactor = reactor
        self.is_locked = is_locked
        self.next_pending = False
        
    def acquire(self):
        if self.is_locked:
            self.next_pending = True
            self.reactor.pause(self.reactor.monotonic() + 99999999.9)
        self.is_locked = True
        
    def release(self):
        if not self.is_locked:
            raise RuntimeError("Mutex not locked")
        self.is_locked = False
        if self.next_pending:
            self.next_pending = False
            self.reactor.update_timer(self.reactor.NOW)

class Reactor:
    """
    Event-driven reactor for handling I/O, timers, and callbacks.
    
    This is the main event loop for AKlippy, handling:
    - Timer callbacks for periodic tasks
    - File descriptor I/O (serial, CAN, etc.)
    - Thread synchronization
    - Graceful shutdown
    """
    
    NOW = 0.
    NEVER = 9999999999999999.
    
    def __init__(self):
        self._timers = []
        self._fd_handlers = {}
        self._g_dispatch = {}
        self.monotonic = time.monotonic
        self._last_gc_time = self.monotonic()
        self._is_shutdown = False
        self._shutdown_msg = ""
        self._async_shutdown_msg = ""
        
        # Threading support
        self._thread_lock = threading.Lock()
        self._async_timer = None
        
        # Logging
        self.logger = logging.getLogger('reactor')
        
    def get_gc_stats(self):
        """Get garbage collection statistics"""
        return ""  # Simplified for now
        
    def _sys_pause(self, waketime):
        """System-level pause using select()"""
        if self._fd_handlers:
            fd_list = list(self._fd_handlers.keys())
            timeout = max(0., min(1., waketime - self.monotonic()))
            try:
                ready, _, _ = select.select(fd_list, [], [], timeout)
                for fd in ready:
                    handler = self._fd_handlers.get(fd)
                    if handler is not None:
                        handler.callback(self.monotonic())
            except (select.error, OSError) as e:
                if e.errno != errno.EINTR:
                    raise
        else:
            # No file descriptors, just sleep
            delay = waketime - self.monotonic()
            if delay > 0.:
                time.sleep(delay)
                
    def pause(self, waketime):
        """Pause execution until waketime or I/O event"""
        if self._is_shutdown:
            return
            
        # Process any pending timers
        self._dispatch_loop(waketime)
        
        # System pause
        self._sys_pause(waketime)
        
    def _dispatch_loop(self, waketime):
        """Process timer callbacks until waketime"""
        while self._timers:
            timer = self._timers[0]
            if timer.waketime > waketime:
                break
                
            heapq.heappop(self._timers)
            try:
                timer.callback(timer.waketime)
            except Exception as e:
                self.logger.exception("Timer callback error: %s", e)
                self.request_exit("Timer callback error")
                
    def run(self):
        """Main reactor loop"""
        self.logger.info("Starting AKlippy reactor")
        
        try:
            while not self._is_shutdown:
                waketime = self.NEVER
                if self._timers:
                    waketime = self._timers[0].waketime
                    
                self.pause(waketime)
                
                # Periodic garbage collection check
                curtime = self.monotonic()
                if curtime > self._last_gc_time + 1.0:
                    self._last_gc_time = curtime
                    # Could add GC stats logging here
                    
        except KeyboardInterrupt:
            self.logger.info("Keyboard interrupt received")
            self.request_exit("Keyboard interrupt")
        except Exception as e:
            self.logger.exception("Reactor error: %s", e)
            self.request_exit("Reactor error")
            
        self.logger.info("Reactor shutdown: %s", self._shutdown_msg)
        
    def register_timer(self, callback, waketime=NOW):
        """Register a timer callback"""
        if waketime == self.NOW:
            waketime = self.monotonic()
        timer = ReactorTimer(callback, waketime)
        heapq.heappush(self._timers, timer)
        return timer
        
    def update_timer(self, timer, waketime=NOW):
        """Update an existing timer"""
        if waketime == self.NOW:
            waketime = self.monotonic()
        timer.waketime = waketime
        heapq.heappush(self._timers, timer)
        
    def register_fd(self, fd, callback):
        """Register a file descriptor for I/O monitoring"""
        handler = ReactorFileHandler(fd, callback)
        self._fd_handlers[fd] = handler
        return handler
        
    def unregister_fd(self, handler):
        """Unregister a file descriptor handler"""
        self._fd_handlers.pop(handler.fd, None)
        
    def register_callback(self, callback, waketime=NOW):
        """Register a one-time callback"""
        def timer_callback(eventtime):
            try:
                callback(eventtime)
            except Exception as e:
                self.logger.exception("Callback error: %s", e)
                self.request_exit("Callback error")
        return self.register_timer(timer_callback, waketime)
        
    def register_async_callback(self, callback):
        """Register an async callback from another thread"""
        with self._thread_lock:
            if self._async_timer is None:
                self._async_timer = self.register_timer(self._async_flush)
            self._g_dispatch[callback] = True
            
    def _async_flush(self, eventtime):
        """Flush async callbacks"""
        with self._thread_lock:
            g_dispatch = self._g_dispatch
            self._g_dispatch = {}
            self._async_timer = None
            
        for callback in g_dispatch:
            try:
                callback(eventtime)
            except Exception as e:
                self.logger.exception("Async callback error: %s", e)
                self.request_exit("Async callback error")
                
    def create_mutex(self, is_locked=False):
        """Create a mutex for thread synchronization"""
        return ReactorMutex(self, is_locked)
        
    def request_exit(self, reason=""):
        """Request reactor shutdown"""
        self._shutdown_msg = reason
        self._is_shutdown = True
        
    def request_exit_atexit(self, reason=""):
        """Request shutdown from signal handler"""
        self._async_shutdown_msg = reason
        self.register_async_callback(self._handle_async_shutdown)
        
    def _handle_async_shutdown(self, eventtime):
        """Handle async shutdown request"""
        self.request_exit(self._async_shutdown_msg)
        
    def is_shutdown(self):
        """Check if reactor is shutting down"""
        return self._is_shutdown
