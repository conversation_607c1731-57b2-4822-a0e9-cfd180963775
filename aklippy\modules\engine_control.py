"""
Engine Control Module

Main engine control logic and coordination between subsystems.
"""

import logging
import time
import math

class EngineControl:
    """
    Main engine control module that coordinates all engine management functions.
    
    This module handles:
    - Engine state monitoring
    - Load calculation
    - RPM management
    - Coordination between fuel and ignition systems
    """
    
    def __init__(self, config):
        self.config = config
        self.logger = logging.getLogger('engine_control')
        
        # Engine configuration
        self.cylinder_count = config.getint('cylinders', 4)
        self.displacement_cc = config.getint('displacement', 2000)
        self.firing_order = config.getintlist('firing_order', [1, 3, 4, 2])
        self.compression_ratio = config.getfloat('compression_ratio', 10.5)
        self.redline_rpm = config.getint('redline_rpm', 7000)
        
        # Engine state
        self.rpm = 0
        self.load_percent = 0
        self.coolant_temp = 20  # °C
        self.intake_temp = 20   # °C
        self.map_pressure = 100 # kPa (atmospheric)
        self.tps_percent = 0    # Throttle position
        self.afr = 14.7         # Air-fuel ratio
        self.engine_running = False
        self.sync_achieved = False
        
        # Timing
        self.crank_position = 0  # degrees
        self.current_cylinder = 1
        self.last_update_time = 0
        
        # References to other modules
        self.mcu = None
        self.fuel_injection = None
        self.ignition_control = None
        self.sensor_manager = None
        
        self.logger.info("Engine control initialized: %d cylinders, %d cc",
                        self.cylinder_count, self.displacement_cc)
        
    def setup(self, aklippy):
        """Setup engine control with AKlippy instance"""
        self.aklippy = aklippy
        self.mcu = aklippy.lookup_object('mcu')
        
        # Get references to other modules
        self.fuel_injection = aklippy.lookup_object('fuel_injection', None)
        self.ignition_control = aklippy.lookup_object('ignition_control', None)
        self.sensor_manager = aklippy.lookup_object('sensor_manager', None)
        
        # Register for periodic updates
        self.aklippy.reactor.register_timer(self._update_callback, 
                                           self.aklippy.reactor.NOW)
        
    def _update_callback(self, eventtime):
        """Periodic update callback"""
        self.update(eventtime)
        return eventtime + 0.01  # Update every 10ms
        
    def update(self, eventtime):
        """Update engine control logic"""
        self.last_update_time = eventtime
        
        # Update engine state from sensors
        self._update_engine_state()
        
        # Calculate engine load
        self._calculate_load()
        
        # Check if engine is running
        self._check_engine_running()
        
        # Update subsystems if engine is running
        if self.engine_running and self.sync_achieved:
            self._update_fuel_control()
            self._update_ignition_control()
            
    def _update_engine_state(self):
        """Update engine state from sensor readings"""
        if self.sensor_manager:
            # Get sensor readings
            sensors = self.sensor_manager.get_all_readings()
            
            self.coolant_temp = sensors.get('coolant_temp', self.coolant_temp)
            self.intake_temp = sensors.get('intake_temp', self.intake_temp)
            self.map_pressure = sensors.get('map_pressure', self.map_pressure)
            self.tps_percent = sensors.get('tps_percent', self.tps_percent)
            
            # Get engine position data from MCU
            # This would come from the MCU's engine position sensors
            
    def _calculate_load(self):
        """Calculate engine load percentage"""
        # Simple load calculation based on MAP and TPS
        # More sophisticated algorithms would consider airflow, etc.
        
        # Normalize MAP to atmospheric pressure
        map_normalized = self.map_pressure / 100.0  # Assume 100 kPa = atmospheric
        
        # Combine MAP and TPS for load calculation
        self.load_percent = min(100, (map_normalized * 50) + (self.tps_percent * 0.5))
        
    def _check_engine_running(self):
        """Check if engine is running based on RPM"""
        # Engine is considered running if RPM > 200
        self.engine_running = self.rpm > 200
        
        # Log engine state changes
        if self.engine_running and not hasattr(self, '_was_running'):
            self.logger.info("Engine started - RPM: %d", self.rpm)
            self._was_running = True
        elif not self.engine_running and hasattr(self, '_was_running'):
            self.logger.info("Engine stopped")
            delattr(self, '_was_running')
            
    def _update_fuel_control(self):
        """Update fuel injection control"""
        if self.fuel_injection:
            # Calculate fuel requirements
            fuel_pulse_width = self._calculate_fuel_pulse_width()
            
            # Update fuel injection system
            self.fuel_injection.set_pulse_width(fuel_pulse_width)
            self.fuel_injection.set_timing_offset(self._calculate_fuel_timing())
            
    def _update_ignition_control(self):
        """Update ignition control"""
        if self.ignition_control:
            # Calculate ignition timing
            timing_advance = self._calculate_ignition_timing()
            dwell_time = self._calculate_dwell_time()
            
            # Update ignition system
            self.ignition_control.set_timing_advance(timing_advance)
            self.ignition_control.set_dwell_time(dwell_time)
            
    def _calculate_fuel_pulse_width(self):
        """Calculate fuel injection pulse width"""
        # Base fuel calculation
        # This is a simplified algorithm - real ECUs use complex 3D maps
        
        # Base pulse width from lookup table (simplified)
        base_pulse = self._lookup_fuel_map(self.rpm, self.load_percent)
        
        # Temperature corrections
        temp_correction = self._get_temperature_correction()
        
        # Acceleration enrichment
        accel_correction = self._get_acceleration_enrichment()
        
        # Closed-loop correction (if O2 sensor available)
        o2_correction = self._get_o2_correction()
        
        # Calculate final pulse width
        pulse_width = base_pulse * temp_correction * accel_correction * o2_correction
        
        return max(0.5, min(50.0, pulse_width))  # Clamp between 0.5ms and 50ms
        
    def _calculate_fuel_timing(self):
        """Calculate fuel injection timing offset"""
        # Fuel timing is typically before intake valve opening
        # This is a simplified calculation
        return 10.0  # degrees BTDC
        
    def _calculate_ignition_timing(self):
        """Calculate ignition timing advance"""
        # Base timing from lookup table
        base_timing = self._lookup_ignition_map(self.rpm, self.load_percent)
        
        # Temperature corrections
        temp_correction = -0.1 * (self.intake_temp - 20)  # Retard for hot air
        
        # Knock correction (if knock sensor available)
        knock_correction = 0  # Would be implemented with knock sensor
        
        # Calculate final timing
        timing_advance = base_timing + temp_correction + knock_correction
        
        return max(-10, min(50, timing_advance))  # Clamp between -10 and 50 degrees
        
    def _calculate_dwell_time(self):
        """Calculate ignition coil dwell time"""
        # Dwell time calculation based on RPM and battery voltage
        # Higher RPM needs longer dwell, higher voltage needs less dwell
        
        base_dwell = 3.0  # ms
        rpm_factor = 1.0 + (self.rpm - 1000) / 10000.0  # Increase with RPM
        voltage_factor = 12.0 / 12.0  # Adjust for battery voltage (simplified)
        
        dwell_time = base_dwell * rpm_factor / voltage_factor
        
        return max(1.0, min(8.0, dwell_time))  # Clamp between 1ms and 8ms
        
    def _lookup_fuel_map(self, rpm, load):
        """Lookup fuel pulse width from map"""
        # Simplified 2D interpolation
        # Real implementation would use proper 3D lookup tables
        
        # Base fuel map (RPM vs Load) - pulse width in ms
        fuel_map = {
            (500, 20): 2.0, (500, 50): 3.0, (500, 80): 4.5,
            (1500, 20): 2.2, (1500, 50): 3.5, (1500, 80): 5.0,
            (3000, 20): 2.5, (3000, 50): 4.0, (3000, 80): 6.0,
            (5000, 20): 3.0, (5000, 50): 4.5, (5000, 80): 7.0,
        }
        
        # Find closest map points and interpolate
        return self._interpolate_map(fuel_map, rpm, load, default=3.0)
        
    def _lookup_ignition_map(self, rpm, load):
        """Lookup ignition timing from map"""
        # Simplified ignition timing map (RPM vs Load) - degrees BTDC
        ignition_map = {
            (500, 20): 10, (500, 50): 8, (500, 80): 6,
            (1500, 20): 15, (1500, 50): 12, (1500, 80): 10,
            (3000, 20): 25, (3000, 50): 20, (3000, 80): 15,
            (5000, 20): 35, (5000, 50): 28, (5000, 80): 22,
        }
        
        return self._interpolate_map(ignition_map, rpm, load, default=15)
        
    def _interpolate_map(self, map_data, rpm, load, default=0):
        """Simple 2D map interpolation"""
        # This is a very simplified interpolation
        # Real ECUs use sophisticated interpolation algorithms
        
        best_match = None
        min_distance = float('inf')
        
        for (map_rpm, map_load), value in map_data.items():
            distance = abs(rpm - map_rpm) + abs(load - map_load)
            if distance < min_distance:
                min_distance = distance
                best_match = value
                
        return best_match if best_match is not None else default
        
    def _get_temperature_correction(self):
        """Get temperature-based fuel correction"""
        # Cold engine needs more fuel
        if self.coolant_temp < 60:
            return 1.0 + (60 - self.coolant_temp) * 0.01  # 1% per degree below 60°C
        return 1.0
        
    def _get_acceleration_enrichment(self):
        """Get acceleration enrichment correction"""
        # This would track TPS rate of change
        # Simplified for now
        return 1.0
        
    def _get_o2_correction(self):
        """Get oxygen sensor correction"""
        # This would implement closed-loop fuel control
        # Simplified for now
        return 1.0
        
    def set_rpm(self, rpm):
        """Set current engine RPM"""
        self.rpm = rpm
        
    def set_crank_position(self, position):
        """Set current crankshaft position"""
        self.crank_position = position
        
    def set_sync_achieved(self, sync):
        """Set engine synchronization status"""
        self.sync_achieved = sync
        if sync:
            self.logger.info("Engine synchronization achieved")
        else:
            self.logger.warning("Engine synchronization lost")
            
    def get_status(self):
        """Get engine control status"""
        return {
            'rpm': self.rpm,
            'load_percent': self.load_percent,
            'coolant_temp': self.coolant_temp,
            'intake_temp': self.intake_temp,
            'map_pressure': self.map_pressure,
            'tps_percent': self.tps_percent,
            'afr': self.afr,
            'engine_running': self.engine_running,
            'sync_achieved': self.sync_achieved,
            'crank_position': self.crank_position,
            'current_cylinder': self.current_cylinder
        }

def load_config(config):
    """Load engine control module"""
    return EngineControl(config)
