"""
Fuel Injection Control Module

Handles fuel injection timing, pulse width calculation, and injector control.
"""

import logging
import time

class FuelInjection:
    """
    Fuel injection control module.
    
    Manages fuel injector timing, pulse width calculation, and
    coordination with engine position for sequential injection.
    """
    
    def __init__(self, config):
        self.config = config
        self.logger = logging.getLogger('fuel_injection')
        
        # Fuel system configuration
        self.injector_count = config.getint('injector_count', 4)
        self.injector_pins = config.getlist('injector_pins', ['PB0', 'PB1', 'PB2', 'PB3'])
        self.injector_flow_rate = config.getfloat('injector_flow_rate', 440)  # cc/min
        self.fuel_pressure = config.getfloat('fuel_pressure', 3.0)  # bar
        self.dead_time = config.getfloat('dead_time', 1.2)  # ms
        
        # Fuel pump configuration
        self.fuel_pump_pin = config.get('fuel_pump_pin', 'PC0')
        self.fuel_pump_prime_time = config.getfloat('fuel_pump_prime_time', 2.0)
        
        # Injection parameters
        self.pulse_width = 2.0  # ms
        self.timing_offset = 10.0  # degrees BTDC
        self.injection_mode = 'sequential'  # sequential, batch, or simultaneous
        
        # Injector states
        self.injectors = []
        for i in range(self.injector_count):
            self.injectors.append({
                'id': i,
                'pin': self.injector_pins[i] if i < len(self.injector_pins) else f'PB{i}',
                'active': False,
                'last_injection_time': 0,
                'pulse_width': self.pulse_width,
                'timing_offset': self.timing_offset
            })
            
        # Fuel pump state
        self.fuel_pump_active = False
        self.fuel_pump_primed = False
        self.fuel_pump_prime_start = 0
        
        # MCU commands
        self.mcu = None
        self.injector_commands = []
        self.fuel_pump_command = None
        
        self.logger.info("Fuel injection initialized: %d injectors, %.1f cc/min flow rate",
                        self.injector_count, self.injector_flow_rate)
        
    def setup(self, aklippy):
        """Setup fuel injection with AKlippy instance"""
        self.aklippy = aklippy
        self.mcu = aklippy.lookup_object('mcu')
        
        # Configure injectors on MCU
        self._configure_injectors()
        
        # Configure fuel pump
        self._configure_fuel_pump()
        
        # Register for engine events
        engine_control = aklippy.lookup_object('engine_control', None)
        if engine_control:
            # Would register for engine position events
            pass
            
    def _configure_injectors(self):
        """Configure injectors on MCU"""
        if not self.mcu:
            return
            
        for injector in self.injectors:
            # Configure injector pin as output
            cmd = self.mcu.lookup_command("config_injector oid=%c pin=%u flow_rate=%u dead_time=%u")
            self.injector_commands.append(cmd)
            
            # Send configuration to MCU
            pin_num = self._parse_pin(injector['pin'])
            cmd.send([injector['id'], pin_num, 
                     int(self.injector_flow_rate), int(self.dead_time * 1000)])
                     
            self.logger.debug("Configured injector %d on pin %s", 
                            injector['id'], injector['pin'])
                            
    def _configure_fuel_pump(self):
        """Configure fuel pump on MCU"""
        if not self.mcu:
            return
            
        # Configure fuel pump as digital output
        cmd = self.mcu.lookup_command("config_digital_out oid=%c pin=%u value=%c")
        self.fuel_pump_command = cmd
        
        pin_num = self._parse_pin(self.fuel_pump_pin)
        cmd.send([255, pin_num, 0])  # Use ID 255 for fuel pump, start OFF
        
        self.logger.debug("Configured fuel pump on pin %s", self.fuel_pump_pin)
        
    def _parse_pin(self, pin_str):
        """Parse pin string (e.g., 'PB0') to pin number"""
        # Simplified pin parsing - real implementation would be more robust
        if len(pin_str) >= 3:
            port = ord(pin_str[1]) - ord('A')  # A=0, B=1, C=2, etc.
            pin = int(pin_str[2:])
            return (port << 4) | pin
        return 0
        
    def start_fuel_pump(self):
        """Start fuel pump and begin priming"""
        if self.fuel_pump_active:
            return
            
        self.logger.info("Starting fuel pump")
        self.fuel_pump_active = True
        self.fuel_pump_prime_start = time.time()
        
        # Send command to MCU to turn on fuel pump
        if self.fuel_pump_command:
            pin_num = self._parse_pin(self.fuel_pump_pin)
            self.fuel_pump_command.send([255, pin_num, 1])
            
    def stop_fuel_pump(self):
        """Stop fuel pump"""
        if not self.fuel_pump_active:
            return
            
        self.logger.info("Stopping fuel pump")
        self.fuel_pump_active = False
        self.fuel_pump_primed = False
        
        # Send command to MCU to turn off fuel pump
        if self.fuel_pump_command:
            pin_num = self._parse_pin(self.fuel_pump_pin)
            self.fuel_pump_command.send([255, pin_num, 0])
            
    def update(self, eventtime):
        """Update fuel injection system"""
        # Check fuel pump priming
        if (self.fuel_pump_active and not self.fuel_pump_primed and
            eventtime - self.fuel_pump_prime_start >= self.fuel_pump_prime_time):
            self.fuel_pump_primed = True
            self.logger.info("Fuel pump primed")
            
    def set_pulse_width(self, pulse_width_ms):
        """Set fuel injection pulse width"""
        self.pulse_width = max(0.1, min(50.0, pulse_width_ms))
        
        # Update all injectors
        for injector in self.injectors:
            injector['pulse_width'] = self.pulse_width
            
    def set_timing_offset(self, timing_offset_deg):
        """Set fuel injection timing offset"""
        self.timing_offset = timing_offset_deg
        
        # Update all injectors
        for injector in self.injectors:
            injector['timing_offset'] = self.timing_offset
            
    def schedule_injection(self, injector_id, timing_us, duration_us=None):
        """Schedule fuel injection for specific injector"""
        if injector_id >= self.injector_count:
            self.logger.error("Invalid injector ID: %d", injector_id)
            return
            
        if not self.fuel_pump_primed:
            self.logger.warning("Fuel pump not primed, skipping injection")
            return
            
        # Use configured pulse width if duration not specified
        if duration_us is None:
            duration_us = int(self.injectors[injector_id]['pulse_width'] * 1000)
            
        # Send injection command to MCU
        if self.mcu and injector_id < len(self.injector_commands):
            cmd = self.mcu.lookup_command("schedule_injection oid=%c clock=%u duration=%u")
            cmd.send([injector_id, timing_us, duration_us])
            
            self.injectors[injector_id]['last_injection_time'] = time.time()
            self.injectors[injector_id]['active'] = True
            
            self.logger.debug("Scheduled injection: injector %d, timing %d us, duration %d us",
                            injector_id, timing_us, duration_us)
                            
    def schedule_sequential_injection(self, crank_position, rpm):
        """Schedule sequential injection based on engine position"""
        if not self.fuel_pump_primed:
            return
            
        # Calculate which cylinder should fire next
        # This is a simplified calculation
        degrees_per_cylinder = 720 / self.injector_count  # 4-stroke cycle
        
        for i, injector in enumerate(self.injectors):
            # Calculate injection timing for this cylinder
            injection_angle = (i * degrees_per_cylinder + self.timing_offset) % 720
            
            # Check if it's time to inject for this cylinder
            if abs(crank_position - injection_angle) < 5:  # 5 degree window
                # Calculate timing in microseconds
                timing_us = int(time.time() * 1000000)  # Current time
                duration_us = int(injector['pulse_width'] * 1000)
                
                self.schedule_injection(i, timing_us, duration_us)
                
    def schedule_batch_injection(self, timing_us):
        """Schedule batch injection (all injectors simultaneously)"""
        if not self.fuel_pump_primed:
            return
            
        # Calculate injection mask (which injectors to fire)
        injector_mask = (1 << self.injector_count) - 1  # All injectors
        duration_us = int(self.pulse_width * 1000)
        
        # Send batch injection command to MCU
        if self.mcu:
            cmd = self.mcu.lookup_command("batch_injection mask=%c clock=%u duration=%u")
            cmd.send([injector_mask, timing_us, duration_us])
            
            self.logger.debug("Scheduled batch injection: mask 0x%02X, duration %d us",
                            injector_mask, duration_us)
                            
    def calculate_pulse_width(self, load_percent, rpm, corrections=None):
        """Calculate fuel injection pulse width"""
        # Base calculation using injector flow rate and engine requirements
        
        # Calculate required fuel flow (simplified)
        # Real calculation would consider air mass, target AFR, etc.
        base_flow = (load_percent / 100.0) * (rpm / 1000.0) * 0.1  # Simplified
        
        # Convert to pulse width based on injector flow rate
        # Flow rate is in cc/min, convert to cc/ms
        flow_rate_per_ms = self.injector_flow_rate / 60000.0
        
        # Calculate pulse width
        pulse_width = (base_flow / flow_rate_per_ms) + (self.dead_time / 1000.0)
        
        # Apply corrections if provided
        if corrections:
            pulse_width *= corrections.get('temperature', 1.0)
            pulse_width *= corrections.get('acceleration', 1.0)
            pulse_width *= corrections.get('o2_feedback', 1.0)
            
        return max(0.1, min(50.0, pulse_width))
        
    def get_status(self):
        """Get fuel injection system status"""
        return {
            'fuel_pump_active': self.fuel_pump_active,
            'fuel_pump_primed': self.fuel_pump_primed,
            'pulse_width': self.pulse_width,
            'timing_offset': self.timing_offset,
            'injection_mode': self.injection_mode,
            'injector_count': self.injector_count,
            'injector_flow_rate': self.injector_flow_rate,
            'fuel_pressure': self.fuel_pressure,
            'dead_time': self.dead_time,
            'injectors': [
                {
                    'id': inj['id'],
                    'active': inj['active'],
                    'last_injection_time': inj['last_injection_time']
                }
                for inj in self.injectors
            ]
        }
        
    def emergency_stop(self):
        """Emergency stop - disable all fuel injection"""
        self.logger.warning("Emergency stop - disabling fuel injection")
        
        # Stop fuel pump
        self.stop_fuel_pump()
        
        # Disable all injectors
        for injector in self.injectors:
            injector['active'] = False
            
        # Send emergency stop to MCU
        if self.mcu:
            cmd = self.mcu.lookup_command("emergency_stop")
            cmd.send()

def load_config(config):
    """Load fuel injection module"""
    return FuelInjection(config)
