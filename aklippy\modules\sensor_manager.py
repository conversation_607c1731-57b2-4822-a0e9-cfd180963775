"""
Sensor Manager Module

Handles all sensor reading, calibration, and data processing.
"""

import logging
import time
import threading

class SensorManager:
    """
    Sensor management module.
    
    Handles reading and processing of all automotive sensors including:
    - Temperature sensors (coolant, intake air, etc.)
    - Pressure sensors (MAP, oil pressure, fuel pressure)
    - Position sensors (TPS, pedal position)
    - Oxygen sensors
    """
    
    def __init__(self, config):
        self.config = config
        self.logger = logging.getLogger('sensor_manager')
        
        # Sensor configurations
        self.sensors = {}
        self._configure_sensors(config)
        
        # Sensor readings
        self.readings = {}
        self.last_readings = {}
        self.reading_timestamps = {}
        
        # Update timing
        self.update_rate = 100  # Hz
        self.last_update_time = 0
        
        # Threading
        self.lock = threading.Lock()
        
        # MCU reference
        self.mcu = None
        
        self.logger.info("Sensor manager initialized with %d sensors", len(self.sensors))
        
    def _configure_sensors(self, config):
        """Configure sensors from config"""
        # Temperature sensors
        if config.has_option('coolant_temp_pin'):
            self.sensors['coolant_temp'] = {
                'type': 'temperature',
                'pin': config.get('coolant_temp_pin'),
                'min_temp': -40,
                'max_temp': 150,
                'calibration': self._get_temp_calibration('coolant')
            }
            
        if config.has_option('intake_air_temp_pin'):
            self.sensors['intake_temp'] = {
                'type': 'temperature',
                'pin': config.get('intake_air_temp_pin'),
                'min_temp': -40,
                'max_temp': 100,
                'calibration': self._get_temp_calibration('intake')
            }
            
        # Pressure sensors
        if config.has_option('map_sensor_pin'):
            self.sensors['map_pressure'] = {
                'type': 'pressure',
                'pin': config.get('map_sensor_pin'),
                'min_pressure': 10,   # kPa
                'max_pressure': 300,  # kPa
                'calibration': self._get_pressure_calibration('map')
            }
            
        if config.has_option('oil_pressure_pin'):
            self.sensors['oil_pressure'] = {
                'type': 'pressure',
                'pin': config.get('oil_pressure_pin'),
                'min_pressure': 0,
                'max_pressure': 1000,  # kPa
                'calibration': self._get_pressure_calibration('oil')
            }
            
        if config.has_option('fuel_pressure_pin'):
            self.sensors['fuel_pressure'] = {
                'type': 'pressure',
                'pin': config.get('fuel_pressure_pin'),
                'min_pressure': 0,
                'max_pressure': 500,  # kPa
                'calibration': self._get_pressure_calibration('fuel')
            }
            
        # Position sensors
        if config.has_option('throttle_position_pin'):
            self.sensors['tps_percent'] = {
                'type': 'position',
                'pin': config.get('throttle_position_pin'),
                'min_percent': 0,
                'max_percent': 100,
                'calibration': self._get_position_calibration('tps')
            }
            
        # Oxygen sensor
        if config.has_option('o2_sensor_pin'):
            self.sensors['o2_voltage'] = {
                'type': 'oxygen',
                'pin': config.get('o2_sensor_pin'),
                'min_voltage': 0.0,
                'max_voltage': 1.0,
                'calibration': self._get_o2_calibration()
            }
            
    def _get_temp_calibration(self, sensor_type):
        """Get temperature sensor calibration"""
        # Simplified calibration for thermistor-based sensors
        # Real implementation would use proper thermistor equations
        return {
            'type': 'thermistor',
            'r_pullup': 4700,    # Pull-up resistor value
            'r_nominal': 2200,   # Nominal resistance at 25°C
            'temp_nominal': 25,  # Nominal temperature
            'beta': 3950         # Beta coefficient
        }
        
    def _get_pressure_calibration(self, sensor_type):
        """Get pressure sensor calibration"""
        # Linear calibration for most pressure sensors
        calibrations = {
            'map': {'offset': 0.5, 'scale': 250.0},      # 0.5-4.5V = 0-250kPa
            'oil': {'offset': 0.5, 'scale': 1000.0},     # 0.5-4.5V = 0-1000kPa
            'fuel': {'offset': 0.5, 'scale': 500.0}      # 0.5-4.5V = 0-500kPa
        }
        return calibrations.get(sensor_type, {'offset': 0.0, 'scale': 100.0})
        
    def _get_position_calibration(self, sensor_type):
        """Get position sensor calibration"""
        # Linear calibration for position sensors
        return {
            'min_voltage': 0.5,   # Minimum voltage (0% position)
            'max_voltage': 4.5,   # Maximum voltage (100% position)
            'deadband': 0.1       # Deadband for noise filtering
        }
        
    def _get_o2_calibration(self):
        """Get oxygen sensor calibration"""
        return {
            'type': 'narrowband',  # or 'wideband'
            'lean_voltage': 0.1,   # Voltage for lean condition
            'rich_voltage': 0.9,   # Voltage for rich condition
            'stoich_voltage': 0.45 # Voltage for stoichiometric
        }
        
    def setup(self, aklippy):
        """Setup sensor manager with AKlippy instance"""
        self.aklippy = aklippy
        self.mcu = aklippy.lookup_object('mcu')
        
        # Configure sensors on MCU
        self._configure_mcu_sensors()
        
        # Register for periodic updates
        aklippy.reactor.register_timer(self._update_callback, aklippy.reactor.NOW)
        
    def _configure_mcu_sensors(self):
        """Configure sensors on MCU"""
        if not self.mcu:
            return
            
        for sensor_name, sensor_config in self.sensors.items():
            # Configure analog sensor on MCU
            pin_num = self._parse_pin(sensor_config['pin'])
            
            cmd = self.mcu.lookup_command("config_analog_sensor oid=%c pin=%c sample_time=%u sample_count=%c")
            cmd.send([len(self.readings), pin_num, 100, 4])  # 100us sample time, 4 samples average
            
            self.logger.debug("Configured sensor %s on pin %s", sensor_name, sensor_config['pin'])
            
    def _parse_pin(self, pin_str):
        """Parse pin string to pin number"""
        if len(pin_str) >= 3:
            port = ord(pin_str[1]) - ord('A')
            pin = int(pin_str[2:])
            return (port << 4) | pin
        return 0
        
    def _update_callback(self, eventtime):
        """Periodic update callback"""
        self.update(eventtime)
        return eventtime + (1.0 / self.update_rate)  # Update at configured rate
        
    def update(self, eventtime):
        """Update all sensor readings"""
        self.last_update_time = eventtime
        
        # Read all sensors
        for sensor_name, sensor_config in self.sensors.items():
            try:
                raw_value = self._read_raw_sensor(sensor_name, sensor_config)
                calibrated_value = self._calibrate_sensor(sensor_name, sensor_config, raw_value)
                
                with self.lock:
                    self.last_readings[sensor_name] = self.readings.get(sensor_name, 0)
                    self.readings[sensor_name] = calibrated_value
                    self.reading_timestamps[sensor_name] = eventtime
                    
            except Exception as e:
                self.logger.error("Error reading sensor %s: %s", sensor_name, e)
                
    def _read_raw_sensor(self, sensor_name, sensor_config):
        """Read raw sensor value from MCU"""
        # This would query the MCU for the latest sensor reading
        # For now, return a simulated value
        
        if sensor_config['type'] == 'temperature':
            # Simulate temperature reading (ADC value 0-4095)
            if 'coolant' in sensor_name:
                return 2048 + int(time.time() % 100)  # Simulate varying temperature
            else:
                return 2000 + int(time.time() % 50)
                
        elif sensor_config['type'] == 'pressure':
            # Simulate pressure reading
            if 'map' in sensor_name:
                return 1500 + int(time.time() % 500)  # Simulate varying MAP
            else:
                return 2000 + int(time.time() % 200)
                
        elif sensor_config['type'] == 'position':
            # Simulate position reading
            return 1000 + int(time.time() % 1000)  # Simulate varying TPS
            
        elif sensor_config['type'] == 'oxygen':
            # Simulate O2 sensor reading
            return 1800 + int(time.time() % 400)  # Simulate varying O2
            
        return 2048  # Default mid-scale value
        
    def _calibrate_sensor(self, sensor_name, sensor_config, raw_value):
        """Calibrate raw sensor value to engineering units"""
        # Convert 12-bit ADC value (0-4095) to voltage (0-5V)
        voltage = (raw_value / 4095.0) * 5.0
        
        if sensor_config['type'] == 'temperature':
            return self._calibrate_temperature(sensor_config, voltage)
        elif sensor_config['type'] == 'pressure':
            return self._calibrate_pressure(sensor_config, voltage)
        elif sensor_config['type'] == 'position':
            return self._calibrate_position(sensor_config, voltage)
        elif sensor_config['type'] == 'oxygen':
            return self._calibrate_oxygen(sensor_config, voltage)
            
        return voltage
        
    def _calibrate_temperature(self, sensor_config, voltage):
        """Calibrate temperature sensor"""
        cal = sensor_config['calibration']
        
        if cal['type'] == 'thermistor':
            # Simplified thermistor calculation
            # Real implementation would use Steinhart-Hart equation
            
            # Calculate resistance from voltage divider
            if voltage > 0.1:  # Avoid division by zero
                r_thermistor = cal['r_pullup'] * voltage / (5.0 - voltage)
                
                # Simplified temperature calculation
                temp_k = 1.0 / (1.0/298.15 + (1.0/cal['beta']) * 
                               math.log(r_thermistor / cal['r_nominal']))
                temp_c = temp_k - 273.15
                
                return max(sensor_config['min_temp'], 
                          min(sensor_config['max_temp'], temp_c))
                          
        return 20.0  # Default room temperature
        
    def _calibrate_pressure(self, sensor_config, voltage):
        """Calibrate pressure sensor"""
        cal = sensor_config['calibration']
        
        # Linear calibration: pressure = (voltage - offset) * scale / voltage_range
        voltage_range = 4.0  # Typically 0.5-4.5V range
        pressure = ((voltage - cal['offset']) / voltage_range) * cal['scale']
        
        return max(sensor_config['min_pressure'], 
                  min(sensor_config['max_pressure'], pressure))
                  
    def _calibrate_position(self, sensor_config, voltage):
        """Calibrate position sensor"""
        cal = sensor_config['calibration']
        
        # Linear calibration with deadband
        voltage_range = cal['max_voltage'] - cal['min_voltage']
        if voltage_range > 0:
            position = ((voltage - cal['min_voltage']) / voltage_range) * 100.0
            
            # Apply deadband
            if abs(position) < cal['deadband']:
                position = 0.0
            elif abs(position - 100) < cal['deadband']:
                position = 100.0
                
            return max(0, min(100, position))
            
        return 0.0
        
    def _calibrate_oxygen(self, sensor_config, voltage):
        """Calibrate oxygen sensor"""
        cal = sensor_config['calibration']
        
        if cal['type'] == 'narrowband':
            # Return voltage directly for narrowband O2 sensor
            return max(0.0, min(1.0, voltage))
        else:
            # For wideband sensors, would convert to AFR
            return voltage
            
    def get_reading(self, sensor_name):
        """Get current reading for a specific sensor"""
        with self.lock:
            return self.readings.get(sensor_name, 0)
            
    def get_all_readings(self):
        """Get all current sensor readings"""
        with self.lock:
            return self.readings.copy()
            
    def get_sensor_status(self, sensor_name):
        """Get status of a specific sensor"""
        if sensor_name not in self.sensors:
            return None
            
        with self.lock:
            current_time = time.time()
            last_update = self.reading_timestamps.get(sensor_name, 0)
            
            return {
                'name': sensor_name,
                'type': self.sensors[sensor_name]['type'],
                'current_value': self.readings.get(sensor_name, 0),
                'last_value': self.last_readings.get(sensor_name, 0),
                'last_update': last_update,
                'age_seconds': current_time - last_update,
                'is_valid': (current_time - last_update) < 1.0  # Valid if updated within 1 second
            }
            
    def get_status(self):
        """Get overall sensor manager status"""
        with self.lock:
            current_time = time.time()
            valid_sensors = 0
            
            for sensor_name in self.sensors:
                last_update = self.reading_timestamps.get(sensor_name, 0)
                if (current_time - last_update) < 1.0:
                    valid_sensors += 1
                    
            return {
                'total_sensors': len(self.sensors),
                'valid_sensors': valid_sensors,
                'update_rate': self.update_rate,
                'last_update_time': self.last_update_time,
                'readings': self.readings.copy()
            }

def load_config(config):
    """Load sensor manager module"""
    return SensorManager(config)
