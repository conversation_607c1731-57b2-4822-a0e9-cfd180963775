"""
Message Protocol - Binary message encoding/decoding

Implements the AKlippy binary protocol for MCU communication.
"""

import struct
import zlib
import logging

class VLQ:
    """Variable Length Quantity encoding/decoding"""
    
    @staticmethod
    def encode(value):
        """Encode an integer as VLQ"""
        if value >= 0:
            if value < 0x40:
                return bytes([value])
            elif value < 0x2000:
                return bytes([0x80 | (value >> 8), value & 0xff])
            elif value < 0x100000:
                return bytes([0xc0 | (value >> 16), (value >> 8) & 0xff, value & 0xff])
            elif value < 0x8000000:
                return bytes([0xe0 | (value >> 24), (value >> 16) & 0xff, 
                             (value >> 8) & 0xff, value & 0xff])
            else:
                return bytes([0xf0, (value >> 24) & 0xff, (value >> 16) & 0xff,
                             (value >> 8) & 0xff, value & 0xff])
        else:
            # Negative values
            value = -value - 1
            if value < 0x20:
                return bytes([0x60 | value])
            elif value < 0x1000:
                return bytes([0xa0 | (value >> 8), value & 0xff])
            elif value < 0x80000:
                return bytes([0xd0 | (value >> 16), (value >> 8) & 0xff, value & 0xff])
            elif value < 0x4000000:
                return bytes([0xf0 | (value >> 24), (value >> 16) & 0xff,
                             (value >> 8) & 0xff, value & 0xff])
            else:
                return bytes([0xf8, (value >> 24) & 0xff, (value >> 16) & 0xff,
                             (value >> 8) & 0xff, value & 0xff])
    
    @staticmethod
    def decode(data, offset=0):
        """Decode VLQ from data at offset, return (value, new_offset)"""
        if offset >= len(data):
            raise ValueError("VLQ decode: insufficient data")
            
        first_byte = data[offset]
        offset += 1
        
        if first_byte < 0x40:
            return first_byte, offset
        elif first_byte < 0x60:
            return -(first_byte - 0x40) - 1, offset
        elif first_byte < 0x80:
            return first_byte - 0x40, offset
        elif first_byte < 0xa0:
            if offset >= len(data):
                raise ValueError("VLQ decode: insufficient data")
            return ((first_byte & 0x1f) << 8) | data[offset], offset + 1
        elif first_byte < 0xc0:
            if offset >= len(data):
                raise ValueError("VLQ decode: insufficient data")
            value = ((first_byte & 0x1f) << 8) | data[offset]
            return -value - 1, offset + 1
        elif first_byte < 0xe0:
            if offset + 1 >= len(data):
                raise ValueError("VLQ decode: insufficient data")
            value = ((first_byte & 0x1f) << 16) | (data[offset] << 8) | data[offset + 1]
            return value, offset + 2
        elif first_byte < 0xf0:
            if offset + 1 >= len(data):
                raise ValueError("VLQ decode: insufficient data")
            value = ((first_byte & 0x1f) << 16) | (data[offset] << 8) | data[offset + 1]
            return -value - 1, offset + 2
        else:
            # Multi-byte encoding
            if offset + 3 >= len(data):
                raise ValueError("VLQ decode: insufficient data")
            value = (data[offset] << 24) | (data[offset + 1] << 16) | \
                   (data[offset + 2] << 8) | data[offset + 3]
            if first_byte & 0x08:
                value = -value - 1
            return value, offset + 4

class MessageProtocol:
    """
    AKlippy binary message protocol implementation.
    
    Handles message framing, encoding/decoding, and error detection.
    """
    
    SYNC_BYTE = 0x7E
    MIN_MESSAGE_LENGTH = 5
    MAX_MESSAGE_LENGTH = 64
    
    def __init__(self):
        self.logger = logging.getLogger('msgproto')
        self.receive_buffer = bytearray()
        self.sequence_number = 0
        
        # Data dictionary for command/response mapping
        self.command_dict = {}
        self.response_dict = {}
        
    def _calculate_crc(self, data):
        """Calculate CRC-CCITT for message data"""
        crc = 0xFFFF
        for byte in data:
            crc ^= byte << 8
            for _ in range(8):
                if crc & 0x8000:
                    crc = (crc << 1) ^ 0x1021
                else:
                    crc <<= 1
                crc &= 0xFFFF
        return crc
        
    def _create_message_block(self, content):
        """Create a message block with header and trailer"""
        # Calculate length (header + content + trailer)
        length = len(content) + 5
        if length > self.MAX_MESSAGE_LENGTH:
            raise ValueError("Message too long: %d bytes" % length)
            
        # Create header
        sequence = (self.sequence_number & 0x0F) | 0x10
        header = bytes([length, sequence])
        
        # Calculate CRC of header + content
        crc_data = header + content
        crc = self._calculate_crc(crc_data)
        
        # Create complete message
        message = header + content + struct.pack('>H', crc) + bytes([self.SYNC_BYTE])
        
        # Update sequence number
        self.sequence_number = (self.sequence_number + 1) & 0x0F
        
        return message
        
    def encode_message(self, cmd_name, params):
        """Encode a command message"""
        # Get command ID from dictionary
        cmd_id = self.command_dict.get(cmd_name, 0)
        
        # Encode content
        content = VLQ.encode(cmd_id)
        for param in params:
            if isinstance(param, int):
                content += VLQ.encode(param)
            elif isinstance(param, str):
                # String encoding: length + data
                str_bytes = param.encode('utf-8')
                content += VLQ.encode(len(str_bytes))
                content += str_bytes
            elif isinstance(param, (bytes, bytearray)):
                # Binary data: length + data
                content += VLQ.encode(len(param))
                content += param
            else:
                raise ValueError("Unsupported parameter type: %s" % type(param))
                
        return self._create_message_block(content)
        
    def process_data(self, data):
        """Process received data and extract complete messages"""
        self.receive_buffer.extend(data)
        messages = []
        
        while len(self.receive_buffer) >= self.MIN_MESSAGE_LENGTH:
            # Look for sync byte
            sync_pos = self.receive_buffer.find(self.SYNC_BYTE)
            if sync_pos == -1:
                # No sync byte found, keep last few bytes
                if len(self.receive_buffer) > 100:
                    self.receive_buffer = self.receive_buffer[-10:]
                break
                
            # Check if we have a complete message before sync
            if sync_pos >= self.MIN_MESSAGE_LENGTH - 1:
                msg_start = sync_pos - (self.MIN_MESSAGE_LENGTH - 1)
                
                # Extract potential message
                if msg_start >= 0:
                    length = self.receive_buffer[msg_start]
                    if length >= self.MIN_MESSAGE_LENGTH and length <= self.MAX_MESSAGE_LENGTH:
                        if msg_start + length <= len(self.receive_buffer):
                            # Extract complete message
                            msg_data = bytes(self.receive_buffer[msg_start:msg_start + length])
                            
                            # Validate message
                            if self._validate_message(msg_data):
                                messages.append(msg_data)
                                
                            # Remove processed data
                            self.receive_buffer = self.receive_buffer[msg_start + length:]
                            continue
                            
            # Remove data up to sync byte
            self.receive_buffer = self.receive_buffer[sync_pos + 1:]
            
        return messages
        
    def _validate_message(self, msg_data):
        """Validate message CRC and format"""
        if len(msg_data) < self.MIN_MESSAGE_LENGTH:
            return False
            
        # Extract components
        length = msg_data[0]
        sequence = msg_data[1]
        content = msg_data[2:-3]
        crc_bytes = msg_data[-3:-1]
        sync = msg_data[-1]
        
        # Check sync byte
        if sync != self.SYNC_BYTE:
            return False
            
        # Check length
        if length != len(msg_data):
            return False
            
        # Check CRC
        crc_data = msg_data[:-3]  # Header + content
        expected_crc = self._calculate_crc(crc_data)
        actual_crc = struct.unpack('>H', crc_bytes)[0]
        
        if expected_crc != actual_crc:
            self.logger.warning("CRC mismatch: expected 0x%04X, got 0x%04X", 
                              expected_crc, actual_crc)
            return False
            
        return True
        
    def decode_message(self, msg_data):
        """Decode a message and return (cmd_name, params)"""
        if not self._validate_message(msg_data):
            raise ValueError("Invalid message")
            
        # Extract content
        content = msg_data[2:-3]
        
        # Decode command ID
        cmd_id, offset = VLQ.decode(content, 0)
        
        # Look up command name
        cmd_name = None
        for name, id_val in self.command_dict.items():
            if id_val == cmd_id:
                cmd_name = name
                break
                
        if cmd_name is None:
            cmd_name = "unknown_%d" % cmd_id
            
        # Decode parameters
        params = []
        while offset < len(content):
            try:
                param, offset = VLQ.decode(content, offset)
                params.append(param)
            except ValueError:
                break
                
        return cmd_name, params
        
    def set_data_dictionary(self, data_dict):
        """Set the data dictionary for command/response mapping"""
        self.command_dict = data_dict.get('commands', {})
        self.response_dict = data_dict.get('responses', {})
        
        self.logger.info("Data dictionary loaded: %d commands, %d responses",
                        len(self.command_dict), len(self.response_dict))
