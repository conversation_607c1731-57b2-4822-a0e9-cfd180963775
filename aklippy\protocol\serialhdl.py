"""
Serial Handler - Serial communication with MCU

Handles low-level serial communication and message framing.
"""

import logging
import serial
import threading
import time
from .msgproto import MessageProtocol

class SerialReader:
    """
    Serial communication handler for MCU communication.
    
    Manages the serial connection and handles message framing,
    error detection, and retransmission.
    """
    
    def __init__(self, reactor, serialport, baud, canbus_iface=None):
        self.reactor = reactor
        self.serialport = serialport
        self.baud = baud
        self.canbus_iface = canbus_iface
        
        # Logging
        self.logger = logging.getLogger('serial')
        
        # Serial connection
        self.serial = None
        self.serial_lock = threading.Lock()
        
        # Protocol handler
        self.msgproto = MessageProtocol()
        
        # Connection state
        self.is_connected = False
        self.connect_time = 0.
        self.receive_timer = None
        
        # Statistics
        self.bytes_write = 0
        self.bytes_read = 0
        self.bytes_retransmit = 0
        self.bytes_invalid = 0
        
        # Response handlers
        self.response_handlers = {}
        
        self.logger.info("Serial handler initialized: %s at %d baud", 
                        serialport, baud)
        
    def connect(self):
        """Connect to serial port"""
        if self.is_connected:
            return
            
        self.logger.info("Connecting to serial port: %s", self.serialport)
        
        try:
            # Open serial port
            self.serial = serial.Serial(
                port=self.serialport,
                baudrate=self.baud,
                timeout=0,  # Non-blocking
                exclusive=True
            )
            
            # Setup receive timer
            self.receive_timer = self.reactor.register_timer(
                self._handle_receive, self.reactor.NOW)
                
            self.is_connected = True
            self.connect_time = time.time()
            
            self.logger.info("Serial connection established")
            
        except Exception as e:
            self.logger.error("Serial connection failed: %s", e)
            raise
            
    def disconnect(self):
        """Disconnect from serial port"""
        if not self.is_connected:
            return
            
        self.logger.info("Disconnecting from serial port")
        
        with self.serial_lock:
            if self.serial:
                self.serial.close()
                self.serial = None
                
        if self.receive_timer:
            # Cancel receive timer
            pass  # Timer will be cleaned up by reactor
            
        self.is_connected = False
        
    def _handle_receive(self, eventtime):
        """Handle incoming serial data"""
        if not self.is_connected or not self.serial:
            return eventtime + 1.0  # Retry in 1 second
            
        try:
            with self.serial_lock:
                data = self.serial.read(4096)  # Read up to 4KB
                
            if data:
                self.bytes_read += len(data)
                self._process_received_data(data)
                
        except Exception as e:
            self.logger.error("Serial receive error: %s", e)
            self.disconnect()
            return eventtime + 1.0
            
        # Schedule next receive check
        return eventtime + 0.001  # Check every 1ms
        
    def _process_received_data(self, data):
        """Process received data and extract messages"""
        messages = self.msgproto.process_data(data)
        
        for msg in messages:
            try:
                self._handle_message(msg)
            except Exception as e:
                self.logger.error("Message handling error: %s", e)
                self.bytes_invalid += len(msg)
                
    def _handle_message(self, msg):
        """Handle a received message"""
        # Decode message
        cmd_name, params = self.msgproto.decode_message(msg)
        
        # Find response handler
        handler = self.response_handlers.get(cmd_name)
        if handler:
            handler(params)
        else:
            self.logger.warning("No handler for message: %s", cmd_name)
            
    def send_command(self, cmd_name, params):
        """Send a command to the MCU"""
        if not self.is_connected or not self.serial:
            raise Exception("Serial not connected")
            
        try:
            # Encode message
            msg_data = self.msgproto.encode_message(cmd_name, params)
            
            # Send via serial
            with self.serial_lock:
                bytes_sent = self.serial.write(msg_data)
                self.serial.flush()
                
            self.bytes_write += bytes_sent
            self.logger.debug("Sent command: %s (%d bytes)", cmd_name, bytes_sent)
            
        except Exception as e:
            self.logger.error("Send command error: %s", e)
            raise
            
    def register_response(self, callback, wake_printer=True):
        """Register a response callback"""
        # For now, register a generic handler
        # In a full implementation, this would be more sophisticated
        self.response_handlers['generic'] = callback
        
    def register_response_handler(self, cmd_name, callback):
        """Register a specific response handler"""
        self.response_handlers[cmd_name] = callback
        
    def get_stats(self, eventtime):
        """Get serial communication statistics"""
        return {
            'bytes_write': self.bytes_write,
            'bytes_read': self.bytes_read,
            'bytes_retransmit': self.bytes_retransmit,
            'bytes_invalid': self.bytes_invalid,
            'connect_time': self.connect_time
        }
        
    def get_reactor(self):
        """Get the reactor instance"""
        return self.reactor
        
    def get_msgparser(self):
        """Get the message parser"""
        return self.msgproto
        
    def get_serialport(self):
        """Get the serial port name"""
        return self.serialport
        
    def set_clock_est(self, freq, conv_time, conv_clock, last_clock):
        """Set clock estimation parameters"""
        # This would be used for precise timing synchronization
        pass
        
    def get_clock_est(self):
        """Get clock estimation parameters"""
        return (0, 0, 0)  # Simplified for now
