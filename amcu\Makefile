# AMCU Firmware Makefile
# Automotive Microcontroller Unit firmware build system

# Default target
TARGET ?= stm32f4

# Directories
SRC_DIR = src
HAL_DIR = hal
DRIVERS_DIR = drivers
BUILD_DIR = build
OUT_DIR = out

# Compiler settings
CC = arm-none-eabi-gcc
OBJCOPY = arm-none-eabi-objcopy
OBJDUMP = arm-none-eabi-objdump
SIZE = arm-none-eabi-size

# Common flags
CFLAGS = -Wall -Wextra -std=c99 -Os -g
CFLAGS += -ffunction-sections -fdata-sections
CFLAGS += -MMD -MP

# Linker flags
LDFLAGS = -Wl,--gc-sections
LDFLAGS += -Wl,--print-memory-usage

# Target-specific settings
ifeq ($(TARGET),stm32f4)
    MCU = cortex-m4
    CFLAGS += -mcpu=$(MCU) -mthumb -mfloat-abi=hard -mfpu=fpv4-sp-d16
    CFLAGS += -DSTM32F4 -DSTM32F407xx
    LDFLAGS += -mcpu=$(MCU) -mthumb -mfloat-abi=hard -mfpu=fpv4-sp-d16
    LDSCRIPT = hal/stm32f4/stm32f407.ld
    HAL_SRC = hal/stm32f4
endif

ifeq ($(TARGET),stm32f7)
    MCU = cortex-m7
    CFLAGS += -mcpu=$(MCU) -mthumb -mfloat-abi=hard -mfpu=fpv5-d16
    CFLAGS += -DSTM32F7 -DSTM32F767xx
    LDFLAGS += -mcpu=$(MCU) -mthumb -mfloat-abi=hard -mfpu=fpv5-d16
    LDSCRIPT = hal/stm32f7/stm32f767.ld
    HAL_SRC = hal/stm32f7
endif

# Include directories
INCLUDES = -I$(SRC_DIR) -I$(HAL_DIR) -I$(DRIVERS_DIR)
INCLUDES += -I$(HAL_SRC)

# Source files
SOURCES = $(wildcard $(SRC_DIR)/*.c)
SOURCES += $(wildcard $(HAL_DIR)/*.c)
SOURCES += $(wildcard $(HAL_SRC)/*.c)
SOURCES += $(wildcard $(DRIVERS_DIR)/*.c)

# Object files
OBJECTS = $(SOURCES:%.c=$(BUILD_DIR)/%.o)
DEPS = $(OBJECTS:.o=.d)

# Output files
ELF = $(OUT_DIR)/amcu_$(TARGET).elf
HEX = $(OUT_DIR)/amcu_$(TARGET).hex
BIN = $(OUT_DIR)/amcu_$(TARGET).bin
MAP = $(OUT_DIR)/amcu_$(TARGET).map

# Default target
all: $(HEX) $(BIN)

# Create directories
$(BUILD_DIR) $(OUT_DIR):
	mkdir -p $@

# Compile C files
$(BUILD_DIR)/%.o: %.c | $(BUILD_DIR)
	@mkdir -p $(dir $@)
	$(CC) $(CFLAGS) $(INCLUDES) -c $< -o $@

# Link ELF file
$(ELF): $(OBJECTS) | $(OUT_DIR)
	$(CC) $(LDFLAGS) -T$(LDSCRIPT) -Wl,-Map=$(MAP) $^ -o $@
	$(SIZE) $@

# Generate HEX file
$(HEX): $(ELF)
	$(OBJCOPY) -O ihex $< $@

# Generate BIN file
$(BIN): $(ELF)
	$(OBJCOPY) -O binary $< $@

# Clean build files
clean:
	rm -rf $(BUILD_DIR) $(OUT_DIR)

# Flash firmware (requires st-flash or openocd)
flash: $(BIN)
	st-flash write $< 0x8000000

# Debug with GDB
debug: $(ELF)
	arm-none-eabi-gdb $<

# Disassemble
disasm: $(ELF)
	$(OBJDUMP) -d $< > $(OUT_DIR)/amcu_$(TARGET).dis

# Show memory usage
size: $(ELF)
	$(SIZE) -A $<

# Help
help:
	@echo "AKlippy AMCU Firmware Build System"
	@echo ""
	@echo "Targets:"
	@echo "  all      - Build firmware (default)"
	@echo "  clean    - Clean build files"
	@echo "  flash    - Flash firmware to MCU"
	@echo "  debug    - Start GDB debugger"
	@echo "  disasm   - Generate disassembly"
	@echo "  size     - Show memory usage"
	@echo "  help     - Show this help"
	@echo ""
	@echo "Variables:"
	@echo "  TARGET   - Target MCU (stm32f4, stm32f7)"
	@echo ""
	@echo "Examples:"
	@echo "  make TARGET=stm32f4"
	@echo "  make flash TARGET=stm32f7"

# Include dependencies
-include $(DEPS)

.PHONY: all clean flash debug disasm size help
