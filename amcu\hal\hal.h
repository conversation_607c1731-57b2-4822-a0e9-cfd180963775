/**
 * @file hal.h
 * @brief Hardware Abstraction Layer for AMCU firmware
 * 
 * This file defines the generic HAL interface that abstracts
 * hardware-specific implementations across different MCU platforms.
 */

#ifndef HAL_H
#define HAL_H

#include <stdint.h>
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

// =============================================================================
// Type Definitions
// =============================================================================

/**
 * @brief GPIO pin definition
 */
typedef struct {
    uint8_t port;       ///< GPIO port (A=0, B=1, etc.)
    uint8_t pin;        ///< Pin number (0-15)
} gpio_pin_t;

/**
 * @brief GPIO configuration
 */
typedef enum {
    GPIO_MODE_INPUT,
    GPIO_MODE_OUTPUT,
    GPIO_MODE_ALTERNATE,
    GPIO_MODE_ANALOG
} gpio_mode_t;

typedef enum {
    GPIO_PULL_NONE,
    GPIO_PULL_UP,
    GPIO_PULL_DOWN
} gpio_pull_t;

typedef enum {
    GPIO_SPEED_LOW,
    GPIO_SPEED_MEDIUM,
    GPIO_SPEED_HIGH,
    GPIO_SPEED_VERY_HIGH
} gpio_speed_t;

/**
 * @brief ADC channel definition
 */
typedef struct {
    uint8_t adc_unit;   ///< ADC unit number (1, 2, 3, etc.)
    uint8_t channel;    ///< ADC channel number
    uint8_t rank;       ///< Conversion rank in sequence
} adc_channel_t;

/**
 * @brief Timer configuration
 */
typedef struct {
    uint8_t timer_num;  ///< Timer number
    uint32_t prescaler; ///< Timer prescaler
    uint32_t period;    ///< Timer period
} timer_config_t;

/**
 * @brief PWM configuration
 */
typedef struct {
    uint8_t timer_num;  ///< Timer number
    uint8_t channel;    ///< PWM channel
    uint32_t frequency; ///< PWM frequency in Hz
    uint16_t duty_cycle; ///< Duty cycle (0-1000 = 0-100%)
} pwm_config_t;

/**
 * @brief CAN configuration
 */
typedef struct {
    uint32_t bitrate;   ///< CAN bitrate
    uint8_t sjw;        ///< Synchronization jump width
    uint8_t bs1;        ///< Bit segment 1
    uint8_t bs2;        ///< Bit segment 2
} can_config_t;

/**
 * @brief CAN message structure
 */
typedef struct {
    uint32_t id;        ///< CAN message ID
    uint8_t dlc;        ///< Data length code
    uint8_t data[8];    ///< Message data
    bool extended;      ///< Extended ID flag
    bool remote;        ///< Remote frame flag
} can_message_t;

// =============================================================================
// System Functions
// =============================================================================

/**
 * @brief Initialize the HAL
 * @return 0 on success, negative on error
 */
int hal_init(void);

/**
 * @brief Get system clock frequency
 * @return System clock frequency in Hz
 */
uint32_t hal_get_system_clock(void);

/**
 * @brief Get microsecond timestamp
 * @return Timestamp in microseconds
 */
uint32_t hal_get_microseconds(void);

/**
 * @brief Delay for specified microseconds
 * @param us Microseconds to delay
 */
void hal_delay_us(uint32_t us);

/**
 * @brief Delay for specified milliseconds
 * @param ms Milliseconds to delay
 */
void hal_delay_ms(uint32_t ms);

// =============================================================================
// GPIO Functions
// =============================================================================

/**
 * @brief Configure GPIO pin
 * @param pin GPIO pin
 * @param mode GPIO mode
 * @param pull Pull-up/down configuration
 * @param speed GPIO speed
 * @return 0 on success, negative on error
 */
int hal_gpio_config(gpio_pin_t pin, gpio_mode_t mode, 
                   gpio_pull_t pull, gpio_speed_t speed);

/**
 * @brief Set GPIO pin state
 * @param pin GPIO pin
 * @param state Pin state (true = high, false = low)
 */
void hal_gpio_write(gpio_pin_t pin, bool state);

/**
 * @brief Read GPIO pin state
 * @param pin GPIO pin
 * @return Pin state (true = high, false = low)
 */
bool hal_gpio_read(gpio_pin_t pin);

/**
 * @brief Toggle GPIO pin state
 * @param pin GPIO pin
 */
void hal_gpio_toggle(gpio_pin_t pin);

// =============================================================================
// ADC Functions
// =============================================================================

/**
 * @brief Initialize ADC
 * @param adc_unit ADC unit number
 * @return 0 on success, negative on error
 */
int hal_adc_init(uint8_t adc_unit);

/**
 * @brief Configure ADC channel
 * @param channel ADC channel configuration
 * @return 0 on success, negative on error
 */
int hal_adc_config_channel(adc_channel_t channel);

/**
 * @brief Read ADC value
 * @param channel ADC channel
 * @return ADC value (0-4095 for 12-bit ADC)
 */
uint16_t hal_adc_read(adc_channel_t channel);

/**
 * @brief Start ADC conversion
 * @param adc_unit ADC unit number
 */
void hal_adc_start_conversion(uint8_t adc_unit);

/**
 * @brief Check if ADC conversion is complete
 * @param adc_unit ADC unit number
 * @return true if conversion complete
 */
bool hal_adc_is_conversion_complete(uint8_t adc_unit);

// =============================================================================
// Timer Functions
// =============================================================================

/**
 * @brief Initialize timer
 * @param config Timer configuration
 * @return 0 on success, negative on error
 */
int hal_timer_init(timer_config_t config);

/**
 * @brief Start timer
 * @param timer_num Timer number
 */
void hal_timer_start(uint8_t timer_num);

/**
 * @brief Stop timer
 * @param timer_num Timer number
 */
void hal_timer_stop(uint8_t timer_num);

/**
 * @brief Get timer count
 * @param timer_num Timer number
 * @return Current timer count
 */
uint32_t hal_timer_get_count(uint8_t timer_num);

/**
 * @brief Set timer count
 * @param timer_num Timer number
 * @param count Timer count value
 */
void hal_timer_set_count(uint8_t timer_num, uint32_t count);

// =============================================================================
// PWM Functions
// =============================================================================

/**
 * @brief Initialize PWM
 * @param config PWM configuration
 * @return 0 on success, negative on error
 */
int hal_pwm_init(pwm_config_t config);

/**
 * @brief Set PWM duty cycle
 * @param timer_num Timer number
 * @param channel PWM channel
 * @param duty_cycle Duty cycle (0-1000 = 0-100%)
 */
void hal_pwm_set_duty(uint8_t timer_num, uint8_t channel, uint16_t duty_cycle);

/**
 * @brief Start PWM output
 * @param timer_num Timer number
 * @param channel PWM channel
 */
void hal_pwm_start(uint8_t timer_num, uint8_t channel);

/**
 * @brief Stop PWM output
 * @param timer_num Timer number
 * @param channel PWM channel
 */
void hal_pwm_stop(uint8_t timer_num, uint8_t channel);

// =============================================================================
// CAN Functions
// =============================================================================

/**
 * @brief Initialize CAN interface
 * @param can_num CAN interface number
 * @param config CAN configuration
 * @return 0 on success, negative on error
 */
int hal_can_init(uint8_t can_num, can_config_t config);

/**
 * @brief Send CAN message
 * @param can_num CAN interface number
 * @param msg CAN message to send
 * @return 0 on success, negative on error
 */
int hal_can_send(uint8_t can_num, can_message_t *msg);

/**
 * @brief Receive CAN message
 * @param can_num CAN interface number
 * @param msg Buffer for received message
 * @return 0 on success, negative on error
 */
int hal_can_receive(uint8_t can_num, can_message_t *msg);

/**
 * @brief Check if CAN message is available
 * @param can_num CAN interface number
 * @return true if message available
 */
bool hal_can_message_available(uint8_t can_num);

// =============================================================================
// Serial/UART Functions
// =============================================================================

/**
 * @brief Initialize UART
 * @param uart_num UART number
 * @param baudrate Baud rate
 * @return 0 on success, negative on error
 */
int hal_uart_init(uint8_t uart_num, uint32_t baudrate);

/**
 * @brief Send data via UART
 * @param uart_num UART number
 * @param data Data buffer
 * @param length Data length
 * @return Number of bytes sent
 */
int hal_uart_send(uint8_t uart_num, const uint8_t *data, uint16_t length);

/**
 * @brief Receive data via UART
 * @param uart_num UART number
 * @param data Data buffer
 * @param length Buffer length
 * @return Number of bytes received
 */
int hal_uart_receive(uint8_t uart_num, uint8_t *data, uint16_t length);

/**
 * @brief Check if UART data is available
 * @param uart_num UART number
 * @return Number of bytes available
 */
int hal_uart_available(uint8_t uart_num);

#ifdef __cplusplus
}
#endif

#endif // HAL_H
