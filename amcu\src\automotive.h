/**
 * @file automotive.h
 * @brief Automotive control systems interface
 * 
 * This file defines the automotive-specific control systems for the AMCU firmware.
 * It includes engine management, sensor processing, and actuator control.
 */

#ifndef AUTOMOTIVE_H
#define AUTOMOTIVE_H

#include <stdint.h>
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

// =============================================================================
// Constants and Definitions
// =============================================================================

#define MAX_CYLINDERS           12
#define MAX_INJECTORS           12
#define MAX_IGNITION_COILS      12
#define MAX_ANALOG_SENSORS      16
#define MAX_DIGITAL_SENSORS     16
#define MAX_PWM_OUTPUTS         8

// Engine position sensor constants
#define CRANK_TEETH_MAX         120
#define MISSING_TEETH_MAX       4

// Timing constants
#define DEGREES_PER_REVOLUTION  360
#define MICROSECONDS_PER_MINUTE 60000000UL

// =============================================================================
// Type Definitions
// =============================================================================

/**
 * @brief Engine configuration
 */
typedef struct {
    uint8_t cylinder_count;
    uint16_t displacement_cc;
    uint8_t firing_order[MAX_CYLINDERS];
    uint16_t compression_ratio_x10;  // Compression ratio * 10
    uint16_t redline_rpm;
} engine_config_t;

/**
 * @brief Crankshaft position sensor configuration
 */
typedef struct {
    uint8_t pin_port;
    uint8_t pin_number;
    uint8_t teeth_count;
    uint8_t missing_teeth;
    bool trigger_rising_edge;
    uint32_t current_position;       // Current position in degrees * 10
    uint32_t rpm;                    // Current RPM
    uint32_t last_tooth_time;        // Last tooth timestamp
} crank_sensor_t;

/**
 * @brief Camshaft position sensor configuration
 */
typedef struct {
    uint8_t pin_port;
    uint8_t pin_number;
    uint8_t cylinder_count;
    bool trigger_rising_edge;
    uint8_t current_cylinder;        // Current cylinder (1-based)
    uint32_t last_sync_time;         // Last sync timestamp
} cam_sensor_t;

/**
 * @brief Fuel injector configuration
 */
typedef struct {
    uint8_t pin_port;
    uint8_t pin_number;
    uint16_t flow_rate_cc_min;       // Flow rate in cc/min
    uint16_t dead_time_us;           // Dead time in microseconds
    uint32_t pulse_width_us;         // Current pulse width
    uint32_t timing_offset_deg_x10;  // Timing offset in degrees * 10
    bool is_active;
} fuel_injector_t;

/**
 * @brief Ignition coil configuration
 */
typedef struct {
    uint8_t pin_port;
    uint8_t pin_number;
    uint16_t charge_time_us;         // Charge time in microseconds
    uint16_t max_dwell_us;           // Maximum dwell time
    uint32_t timing_advance_deg_x10; // Timing advance in degrees * 10
    bool is_charging;
    uint32_t charge_start_time;
} ignition_coil_t;

/**
 * @brief Analog sensor configuration
 */
typedef struct {
    uint8_t adc_unit;
    uint8_t adc_channel;
    uint16_t min_value;              // Minimum ADC value
    uint16_t max_value;              // Maximum ADC value
    int32_t offset;                  // Calibration offset
    uint32_t scale_factor;           // Scale factor * 1000
    uint32_t last_reading;           // Last sensor reading
    uint32_t last_update_time;       // Last update timestamp
} analog_sensor_t;

/**
 * @brief PWM output configuration
 */
typedef struct {
    uint8_t timer_num;
    uint8_t channel;
    uint32_t frequency_hz;
    uint16_t duty_cycle_x10;         // Duty cycle * 10 (0-1000 = 0-100%)
    bool is_active;
} pwm_output_t;

/**
 * @brief Engine state structure
 */
typedef struct {
    uint32_t rpm;
    uint32_t load_percent_x10;       // Engine load * 10
    int16_t coolant_temp_c;          // Coolant temperature in °C
    int16_t intake_temp_c;           // Intake air temperature in °C
    uint16_t map_kpa;                // Manifold absolute pressure in kPa
    uint16_t tps_percent_x10;        // Throttle position * 10
    uint16_t afr_x100;               // Air-fuel ratio * 100
    uint32_t crank_position_deg_x10; // Crankshaft position * 10
    uint8_t current_cylinder;        // Current cylinder
    bool engine_running;
    bool sync_achieved;              // Cam/crank sync achieved
} engine_state_t;

// =============================================================================
// Global Variables (extern declarations)
// =============================================================================

extern engine_config_t engine_config;
extern engine_state_t engine_state;
extern crank_sensor_t crank_sensor;
extern cam_sensor_t cam_sensor;
extern fuel_injector_t fuel_injectors[MAX_INJECTORS];
extern ignition_coil_t ignition_coils[MAX_IGNITION_COILS];
extern analog_sensor_t analog_sensors[MAX_ANALOG_SENSORS];
extern pwm_output_t pwm_outputs[MAX_PWM_OUTPUTS];

// =============================================================================
// Function Prototypes
// =============================================================================

/**
 * @brief Initialize automotive systems
 * @return 0 on success, negative on error
 */
int automotive_init(void);

/**
 * @brief Update automotive control systems
 * @param current_time Current time in microseconds
 */
void automotive_update(uint32_t current_time);

/**
 * @brief System tick handler for automotive systems
 */
void automotive_systick_handler(void);

/**
 * @brief Emergency stop all automotive systems
 */
void automotive_emergency_stop(void);

/**
 * @brief Check if sensors are OK
 * @return true if all sensors are functioning
 */
bool automotive_are_sensors_ok(void);

/**
 * @brief Check if actuators are OK
 * @return true if all actuators are functioning
 */
bool automotive_are_actuators_ok(void);

// =============================================================================
// Engine Position Functions
// =============================================================================

/**
 * @brief Configure crankshaft position sensor
 * @param pin_port GPIO port
 * @param pin_number GPIO pin number
 * @param teeth_count Number of teeth on crank wheel
 * @param missing_teeth Number of missing teeth
 * @return 0 on success, negative on error
 */
int automotive_config_crank_sensor(uint8_t pin_port, uint8_t pin_number,
                                  uint8_t teeth_count, uint8_t missing_teeth);

/**
 * @brief Configure camshaft position sensor
 * @param pin_port GPIO port
 * @param pin_number GPIO pin number
 * @param cylinder_count Number of cylinders
 * @return 0 on success, negative on error
 */
int automotive_config_cam_sensor(uint8_t pin_port, uint8_t pin_number,
                                uint8_t cylinder_count);

/**
 * @brief Crankshaft position interrupt handler
 */
void automotive_crank_interrupt_handler(void);

/**
 * @brief Camshaft position interrupt handler
 */
void automotive_cam_interrupt_handler(void);

/**
 * @brief Get current engine RPM
 * @return Engine RPM
 */
uint32_t automotive_get_rpm(void);

/**
 * @brief Get current crankshaft position
 * @return Crankshaft position in degrees * 10
 */
uint32_t automotive_get_crank_position(void);

// =============================================================================
// Fuel Injection Functions
// =============================================================================

/**
 * @brief Configure fuel injector
 * @param injector_id Injector ID (0-based)
 * @param pin_port GPIO port
 * @param pin_number GPIO pin number
 * @param flow_rate Flow rate in cc/min
 * @param dead_time Dead time in microseconds
 * @return 0 on success, negative on error
 */
int automotive_config_injector(uint8_t injector_id, uint8_t pin_port,
                              uint8_t pin_number, uint16_t flow_rate,
                              uint16_t dead_time);

/**
 * @brief Schedule fuel injection
 * @param injector_id Injector ID
 * @param timing_us Injection timing in microseconds
 * @param duration_us Injection duration in microseconds
 * @return 0 on success, negative on error
 */
int automotive_schedule_injection(uint8_t injector_id, uint32_t timing_us,
                                 uint32_t duration_us);

/**
 * @brief Calculate fuel injection pulse width
 * @param load_percent Engine load percentage
 * @param rpm Engine RPM
 * @return Pulse width in microseconds
 */
uint32_t automotive_calculate_fuel_pulse_width(uint16_t load_percent, uint32_t rpm);

// =============================================================================
// Ignition Control Functions
// =============================================================================

/**
 * @brief Configure ignition coil
 * @param coil_id Coil ID (0-based)
 * @param pin_port GPIO port
 * @param pin_number GPIO pin number
 * @param charge_time Charge time in microseconds
 * @param max_dwell Maximum dwell time in microseconds
 * @return 0 on success, negative on error
 */
int automotive_config_ignition(uint8_t coil_id, uint8_t pin_port,
                              uint8_t pin_number, uint16_t charge_time,
                              uint16_t max_dwell);

/**
 * @brief Schedule ignition event
 * @param coil_id Coil ID
 * @param timing_us Ignition timing in microseconds
 * @param dwell_us Dwell time in microseconds
 * @return 0 on success, negative on error
 */
int automotive_schedule_ignition(uint8_t coil_id, uint32_t timing_us,
                                uint32_t dwell_us);

/**
 * @brief Calculate ignition timing advance
 * @param load_percent Engine load percentage
 * @param rpm Engine RPM
 * @return Timing advance in degrees * 10
 */
uint32_t automotive_calculate_ignition_timing(uint16_t load_percent, uint32_t rpm);

// =============================================================================
// Sensor Functions
// =============================================================================

/**
 * @brief Configure analog sensor
 * @param sensor_id Sensor ID (0-based)
 * @param adc_unit ADC unit number
 * @param adc_channel ADC channel
 * @param min_value Minimum ADC value
 * @param max_value Maximum ADC value
 * @return 0 on success, negative on error
 */
int automotive_config_analog_sensor(uint8_t sensor_id, uint8_t adc_unit,
                                   uint8_t adc_channel, uint16_t min_value,
                                   uint16_t max_value);

/**
 * @brief Read analog sensor
 * @param sensor_id Sensor ID
 * @return Sensor reading (calibrated value)
 */
uint32_t automotive_read_analog_sensor(uint8_t sensor_id);

/**
 * @brief Update all sensor readings
 */
void automotive_update_sensors(void);

// =============================================================================
// Actuator Functions
// =============================================================================

/**
 * @brief Configure PWM output
 * @param output_id Output ID (0-based)
 * @param timer_num Timer number
 * @param channel PWM channel
 * @param frequency_hz PWM frequency in Hz
 * @return 0 on success, negative on error
 */
int automotive_config_pwm(uint8_t output_id, uint8_t timer_num,
                         uint8_t channel, uint32_t frequency_hz);

/**
 * @brief Set PWM duty cycle
 * @param output_id Output ID
 * @param duty_cycle_x10 Duty cycle * 10 (0-1000)
 * @return 0 on success, negative on error
 */
int automotive_set_pwm_duty(uint8_t output_id, uint16_t duty_cycle_x10);

#ifdef __cplusplus
}
#endif

#endif // AUTOMOTIVE_H
