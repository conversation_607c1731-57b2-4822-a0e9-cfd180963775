/**
 * @file main.c
 * @brief AMCU firmware main entry point
 * 
 * This is the main entry point for the Automotive Microcontroller Unit (AMCU)
 * firmware. It initializes the system and runs the main control loop.
 */

#include <stdint.h>
#include <stdbool.h>
#include <string.h>

#include "hal/hal.h"
#include "protocol.h"
#include "automotive.h"
#include "safety.h"

// =============================================================================
// Global Variables
// =============================================================================

static bool system_initialized = false;
static bool emergency_stop = false;
static uint32_t main_loop_counter = 0;
static uint32_t last_heartbeat_time = 0;

// System status
typedef struct {
    bool mcu_ready;
    bool communication_active;
    bool sensors_ok;
    bool actuators_ok;
    uint32_t uptime_ms;
    uint16_t supply_voltage_mv;
    int16_t mcu_temperature_c;
} system_status_t;

static system_status_t system_status = {0};

// =============================================================================
// Function Prototypes
// =============================================================================

static int system_init(void);
static void main_loop(void);
static void update_system_status(void);
static void handle_emergency_stop(void);
static void send_heartbeat(void);

// =============================================================================
// Main Function
// =============================================================================

/**
 * @brief Main entry point
 * @return Should never return
 */
int main(void)
{
    // Initialize system
    if (system_init() != 0) {
        // System initialization failed - enter safe mode
        safety_enter_safe_mode("System initialization failed");
        while (1) {
            // Minimal safe mode operation
            hal_delay_ms(100);
        }
    }
    
    system_initialized = true;
    system_status.mcu_ready = true;
    
    // Send startup message
    protocol_send_startup_message();
    
    // Main control loop
    while (1) {
        main_loop();
    }
    
    return 0; // Should never reach here
}

// =============================================================================
// System Initialization
// =============================================================================

/**
 * @brief Initialize all system components
 * @return 0 on success, negative on error
 */
static int system_init(void)
{
    int result;
    
    // Initialize HAL
    result = hal_init();
    if (result != 0) {
        return result;
    }
    
    // Initialize communication protocol
    result = protocol_init();
    if (result != 0) {
        return result;
    }
    
    // Initialize automotive subsystems
    result = automotive_init();
    if (result != 0) {
        return result;
    }
    
    // Initialize safety systems
    result = safety_init();
    if (result != 0) {
        return result;
    }
    
    // Configure system tick for 1ms intervals
    // This would be MCU-specific implementation
    
    return 0;
}

// =============================================================================
// Main Control Loop
// =============================================================================

/**
 * @brief Main control loop - runs continuously
 */
static void main_loop(void)
{
    uint32_t current_time = hal_get_microseconds();
    
    // Increment loop counter
    main_loop_counter++;
    
    // Check for emergency stop condition
    if (emergency_stop) {
        handle_emergency_stop();
        return;
    }
    
    // Process incoming communication
    protocol_process_incoming();
    
    // Update automotive control systems
    automotive_update(current_time);
    
    // Update safety monitoring
    safety_update(current_time);
    
    // Update system status
    update_system_status();
    
    // Send periodic heartbeat (every 100ms)
    if (current_time - last_heartbeat_time >= 100000) {
        send_heartbeat();
        last_heartbeat_time = current_time;
    }
    
    // Process outgoing communication
    protocol_process_outgoing();
    
    // Small delay to prevent overwhelming the system
    hal_delay_us(100);
}

// =============================================================================
// System Status and Monitoring
// =============================================================================

/**
 * @brief Update system status information
 */
static void update_system_status(void)
{
    static uint32_t last_update_time = 0;
    uint32_t current_time = hal_get_microseconds();
    
    // Update every 10ms
    if (current_time - last_update_time < 10000) {
        return;
    }
    last_update_time = current_time;
    
    // Update uptime (convert microseconds to milliseconds)
    system_status.uptime_ms = current_time / 1000;
    
    // Check communication status
    system_status.communication_active = protocol_is_communication_active();
    
    // Check sensor status
    system_status.sensors_ok = automotive_are_sensors_ok();
    
    // Check actuator status
    system_status.actuators_ok = automotive_are_actuators_ok();
    
    // Read supply voltage (example - would be ADC reading)
    // system_status.supply_voltage_mv = hal_adc_read_supply_voltage();
    
    // Read MCU temperature (example - would be internal temp sensor)
    // system_status.mcu_temperature_c = hal_read_internal_temperature();
}

/**
 * @brief Handle emergency stop condition
 */
static void handle_emergency_stop(void)
{
    // Disable all actuators immediately
    automotive_emergency_stop();
    
    // Send emergency stop status
    protocol_send_emergency_stop_status();
    
    // Enter safe mode
    safety_enter_safe_mode("Emergency stop activated");
    
    // Wait for reset or recovery command
    hal_delay_ms(10);
}

/**
 * @brief Send periodic heartbeat message
 */
static void send_heartbeat(void)
{
    // Send system status as heartbeat
    protocol_send_system_status(&system_status);
}

// =============================================================================
// Interrupt Handlers and Callbacks
// =============================================================================

/**
 * @brief System tick interrupt handler (1ms)
 */
void systick_handler(void)
{
    // Update automotive timing-critical functions
    automotive_systick_handler();
    
    // Update safety monitoring
    safety_systick_handler();
    
    // Update protocol timing
    protocol_systick_handler();
}

/**
 * @brief Emergency stop interrupt handler
 */
void emergency_stop_handler(void)
{
    emergency_stop = true;
}

/**
 * @brief Communication timeout handler
 */
void communication_timeout_handler(void)
{
    // Handle communication timeout
    system_status.communication_active = false;
    
    // Enter failsafe mode if configured
    if (safety_is_failsafe_enabled()) {
        safety_enter_failsafe_mode("Communication timeout");
    }
}

// =============================================================================
// System Information Functions
// =============================================================================

/**
 * @brief Get system information
 * @return Pointer to system status structure
 */
const system_status_t* get_system_status(void)
{
    return &system_status;
}

/**
 * @brief Get main loop counter
 * @return Main loop counter value
 */
uint32_t get_main_loop_counter(void)
{
    return main_loop_counter;
}

/**
 * @brief Check if system is initialized
 * @return true if system is initialized
 */
bool is_system_initialized(void)
{
    return system_initialized;
}

/**
 * @brief Request emergency stop
 */
void request_emergency_stop(void)
{
    emergency_stop = true;
}
