/**
 * @file protocol.h
 * @brief AMCU communication protocol interface
 * 
 * This file defines the communication protocol interface for the AMCU firmware.
 * It handles the binary protocol communication with the AKlippy host software.
 */

#ifndef PROTOCOL_H
#define PROTOCOL_H

#include <stdint.h>
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

// =============================================================================
// Protocol Constants
// =============================================================================

#define PROTOCOL_SYNC_BYTE          0x7E
#define PROTOCOL_MIN_MESSAGE_LENGTH 5
#define PROTOCOL_MAX_MESSAGE_LENGTH 64
#define PROTOCOL_MAX_PARAMS         16

// =============================================================================
// Message Types
// =============================================================================

/**
 * @brief Protocol message structure
 */
typedef struct {
    uint8_t length;         ///< Message length
    uint8_t sequence;       ///< Sequence number
    uint8_t *content;       ///< Message content
    uint16_t crc;           ///< CRC checksum
    uint8_t sync;           ///< Sync byte
} protocol_message_t;

/**
 * @brief Command parameter structure
 */
typedef struct {
    uint32_t values[PROTOCOL_MAX_PARAMS];
    uint8_t count;
} protocol_params_t;

/**
 * @brief Command handler function type
 */
typedef void (*command_handler_t)(protocol_params_t *params);

/**
 * @brief Command definition structure
 */
typedef struct {
    const char *name;
    uint8_t id;
    command_handler_t handler;
    uint8_t param_count;
} command_def_t;

// =============================================================================
// System Status Structure (forward declaration from main.c)
// =============================================================================

typedef struct {
    bool mcu_ready;
    bool communication_active;
    bool sensors_ok;
    bool actuators_ok;
    uint32_t uptime_ms;
    uint16_t supply_voltage_mv;
    int16_t mcu_temperature_c;
} system_status_t;

// =============================================================================
// Function Prototypes
// =============================================================================

/**
 * @brief Initialize the protocol system
 * @return 0 on success, negative on error
 */
int protocol_init(void);

/**
 * @brief Process incoming messages
 */
void protocol_process_incoming(void);

/**
 * @brief Process outgoing messages
 */
void protocol_process_outgoing(void);

/**
 * @brief System tick handler for protocol timing
 */
void protocol_systick_handler(void);

/**
 * @brief Check if communication is active
 * @return true if communication is active
 */
bool protocol_is_communication_active(void);

/**
 * @brief Send startup message
 */
void protocol_send_startup_message(void);

/**
 * @brief Send system status
 * @param status Pointer to system status structure
 */
void protocol_send_system_status(const system_status_t *status);

/**
 * @brief Send emergency stop status
 */
void protocol_send_emergency_stop_status(void);

/**
 * @brief Send sensor data
 * @param sensor_id Sensor identifier
 * @param timestamp Timestamp in microseconds
 * @param value Sensor value
 */
void protocol_send_sensor_data(uint8_t sensor_id, uint32_t timestamp, uint32_t value);

/**
 * @brief Send engine position data
 * @param timestamp Timestamp in microseconds
 * @param crank_pos Crankshaft position
 * @param cam_pos Camshaft position
 * @param rpm Engine RPM
 */
void protocol_send_engine_position(uint32_t timestamp, uint32_t crank_pos, 
                                 uint8_t cam_pos, uint32_t rpm);

/**
 * @brief Send fault codes
 * @param fault_count Number of fault codes
 * @param fault_codes Array of fault codes
 */
void protocol_send_fault_codes(uint8_t fault_count, const uint16_t *fault_codes);

/**
 * @brief Send log data
 * @param timestamp Timestamp in microseconds
 * @param data_length Length of data
 * @param data Log data buffer
 */
void protocol_send_log_data(uint32_t timestamp, uint16_t data_length, const uint8_t *data);

// =============================================================================
// Command Handlers (implemented in protocol.c)
// =============================================================================

/**
 * @brief Handle identify command
 * @param params Command parameters
 */
void cmd_identify(protocol_params_t *params);

/**
 * @brief Handle get_config command
 * @param params Command parameters
 */
void cmd_get_config(protocol_params_t *params);

/**
 * @brief Handle get_clock command
 * @param params Command parameters
 */
void cmd_get_clock(protocol_params_t *params);

/**
 * @brief Handle set_clock command
 * @param params Command parameters
 */
void cmd_set_clock(protocol_params_t *params);

/**
 * @brief Handle emergency_stop command
 * @param params Command parameters
 */
void cmd_emergency_stop(protocol_params_t *params);

/**
 * @brief Handle reset command
 * @param params Command parameters
 */
void cmd_reset(protocol_params_t *params);

/**
 * @brief Handle config_crank_sensor command
 * @param params Command parameters
 */
void cmd_config_crank_sensor(protocol_params_t *params);

/**
 * @brief Handle config_cam_sensor command
 * @param params Command parameters
 */
void cmd_config_cam_sensor(protocol_params_t *params);

/**
 * @brief Handle config_injector command
 * @param params Command parameters
 */
void cmd_config_injector(protocol_params_t *params);

/**
 * @brief Handle schedule_injection command
 * @param params Command parameters
 */
void cmd_schedule_injection(protocol_params_t *params);

/**
 * @brief Handle config_ignition command
 * @param params Command parameters
 */
void cmd_config_ignition(protocol_params_t *params);

/**
 * @brief Handle schedule_ignition command
 * @param params Command parameters
 */
void cmd_schedule_ignition(protocol_params_t *params);

/**
 * @brief Handle config_analog_sensor command
 * @param params Command parameters
 */
void cmd_config_analog_sensor(protocol_params_t *params);

/**
 * @brief Handle query_sensors command
 * @param params Command parameters
 */
void cmd_query_sensors(protocol_params_t *params);

/**
 * @brief Handle config_pwm command
 * @param params Command parameters
 */
void cmd_config_pwm(protocol_params_t *params);

/**
 * @brief Handle set_pwm command
 * @param params Command parameters
 */
void cmd_set_pwm(protocol_params_t *params);

/**
 * @brief Handle get_status command
 * @param params Command parameters
 */
void cmd_get_status(protocol_params_t *params);

/**
 * @brief Handle get_faults command
 * @param params Command parameters
 */
void cmd_get_faults(protocol_params_t *params);

/**
 * @brief Handle clear_faults command
 * @param params Command parameters
 */
void cmd_clear_faults(protocol_params_t *params);

/**
 * @brief Handle start_logging command
 * @param params Command parameters
 */
void cmd_start_logging(protocol_params_t *params);

/**
 * @brief Handle stop_logging command
 * @param params Command parameters
 */
void cmd_stop_logging(protocol_params_t *params);

#ifdef __cplusplus
}
#endif

#endif // PROTOCOL_H
