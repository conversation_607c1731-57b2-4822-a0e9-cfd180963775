# AKlippy Basic Configuration Example
# This is a basic configuration for a 4-cylinder engine

# MCU Configuration
[mcu]
serial: /dev/ttyUSB0
baud: 250000
mcu_type: stm32f4

# Engine Configuration
[engine]
cylinders: 4
displacement: 2000  # cc
firing_order: 1,3,4,2
compression_ratio: 10.5
redline_rpm: 7000

# Crankshaft Position Sensor
[crank_sensor]
pin: PA0
teeth: 60
missing_teeth: 2
trigger_edge: rising

# Camshaft Position Sensor
[cam_sensor]
pin: PA1
cylinders: 4
trigger_edge: rising

# Fuel System Configuration
[fuel_system]
# Fuel injectors
injector_count: 4
injector_pins: PB0,PB1,PB2,PB3
injector_flow_rate: 440  # cc/min at 3 bar
fuel_pressure: 3.0       # bar
dead_time: 1.2          # ms
# Fuel pump
fuel_pump_pin: PC0
fuel_pump_prime_time: 2.0  # seconds

# Ignition System Configuration
[ignition_system]
# Ignition coils
coil_count: 4
coil_pins: PD0,PD1,PD2,PD3
coil_charge_time: 3.0   # ms
dwell_time: 2.5         # ms
max_dwell_time: 8.0     # ms
# Ignition timing
base_timing: 10         # degrees BTDC
timing_offset: 0        # degrees

# Sensor Configuration
[sensors]
# Temperature sensors
coolant_temp_pin: PA2
intake_air_temp_pin: PA3
# Pressure sensors
map_sensor_pin: PA4
oil_pressure_pin: PA5
fuel_pressure_pin: PA6
# Position sensors
throttle_position_pin: PA7
# Oxygen sensor
o2_sensor_pin: PB4

# Actuator Configuration
[actuators]
# Electronic throttle body
throttle_motor_pins: PC1,PC2
throttle_position_sensor: PA7
# Idle air control
iac_pin: PC3
iac_type: stepper  # or pwm
# Variable valve timing
vvt_pin: PC4

# Engine Control Parameters
[fuel_control]
# Base fuel map (RPM vs Load)
base_fuel_map: |
    # RPM:  500  1000  1500  2000  2500  3000  3500  4000  4500  5000  5500  6000
    # Load: 
    20:     8.0   8.5   9.0   9.5  10.0  10.5  11.0  11.5  12.0  12.5  13.0  13.5
    40:    12.0  12.5  13.0  13.5  14.0  14.5  15.0  15.5  16.0  16.5  17.0  17.5
    60:    16.0  16.5  17.0  17.5  18.0  18.5  19.0  19.5  20.0  20.5  21.0  21.5
    80:    20.0  20.5  21.0  21.5  22.0  22.5  23.0  23.5  24.0  24.5  25.0  25.5
    100:   24.0  24.5  25.0  25.5  26.0  26.5  27.0  27.5  28.0  28.5  29.0  29.5

# Acceleration enrichment
accel_enrich_threshold: 5.0  # %/s TPS change
accel_enrich_amount: 20      # % fuel increase
accel_enrich_decay: 0.5      # decay rate

# Closed loop control
enable_closed_loop: true
target_afr: 14.7
o2_sensor_type: narrowband
closed_loop_min_rpm: 800
closed_loop_max_rpm: 4000

[ignition_control]
# Base ignition timing map (RPM vs Load)
base_timing_map: |
    # RPM:  500  1000  1500  2000  2500  3000  3500  4000  4500  5000  5500  6000
    # Load:
    20:     10    12    14    16    18    20    22    24    26    28    30    32
    40:      8    10    12    14    16    18    20    22    24    26    28    30
    60:      6     8    10    12    14    16    18    20    22    24    26    28
    80:      4     6     8    10    12    14    16    18    20    22    24    26
    100:     2     4     6     8    10    12    14    16    18    20    22    24

# Knock control
enable_knock_control: false
knock_sensor_pin: PB5
knock_retard_amount: 2.0     # degrees per knock event
knock_recovery_rate: 0.1     # degrees per cycle

[idle_control]
# Idle speed control
target_idle_rpm: 800
idle_control_type: iac  # or electronic_throttle
# PID parameters
idle_p_gain: 0.5
idle_i_gain: 0.1
idle_d_gain: 0.05

[rev_limiter]
# Rev limiter settings
soft_limit_rpm: 6500
hard_limit_rpm: 7000
limiter_type: fuel_cut  # or ignition_cut or both

# Data Logging Configuration
[data_logging]
enable_logging: true
log_rate: 100  # Hz
log_channels: rpm,map,tps,coolant_temp,afr,timing_advance
log_file_path: /var/log/aklippy/

# Diagnostic Configuration
[diagnostics]
enable_diagnostics: true
dtc_storage: eeprom
# OBD-II support
enable_obd2: false
obd2_protocol: iso9141

# Safety Configuration
[safety]
# Failsafe settings
enable_failsafe: true
failsafe_rpm_limit: 4000
failsafe_fuel_cut: true
failsafe_ignition_retard: 10  # degrees

# Monitoring thresholds
max_coolant_temp: 110  # °C
max_oil_pressure: 7.0  # bar
min_oil_pressure: 1.0  # bar
max_intake_temp: 60    # °C
