# AKlippy Architecture Specification

## 1. System Overview

AKlippy implements a distributed architecture that separates computational complexity from real-time constraints, inspired by <PERSON><PERSON><PERSON>'s successful approach to 3D printer control.

### 1.1 Core Principles

- **Separation of Concerns**: High-level algorithms run on powerful host, real-time operations on dedicated MCU
- **Deterministic Timing**: Critical automotive functions maintain precise timing through MCU-based control
- **Hardware Abstraction**: Unified interface across different MCU platforms and automotive hardware
- **Modularity**: Pluggable control modules for different automotive functions
- **Safety**: Built-in fault detection and safe-state mechanisms

### 1.2 System Components

```
┌─────────────────────────────────────────────────────────────┐
│                    Host Computer                            │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                AKlippy Host Software                    │ │
│  │  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────┐ │ │
│  │  │   Engine    │ │ Diagnostics │ │    Configuration    │ │ │
│  │  │  Control    │ │ & Logging   │ │    & Tuning        │ │ │
│  │  │  Modules    │ │             │ │                     │ │ │
│  │  └─────────────┘ └─────────────┘ └─────────────────────┘ │ │
│  │  ┌─────────────────────────────────────────────────────┐ │ │
│  │  │           Protocol & Communication Layer            │ │ │
│  │  └─────────────────────────────────────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
                    ┌───────────┴───────────┐
                    │    Communication      │
                    │  (Serial/CAN/USB)     │
                    └───────────┬───────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                 Automotive MCU                              │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                 AMCU Firmware                           │ │
│  │  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────┐ │ │
│  │  │  Real-time  │ │   Sensor    │ │     Actuator        │ │ │
│  │  │ I/O Control │ │ Processing  │ │     Drivers         │ │ │
│  │  └─────────────┘ └─────────────┘ └─────────────────────┘ │ │
│  │  ┌─────────────────────────────────────────────────────┐ │ │
│  │  │          Hardware Abstraction Layer (HAL)           │ │ │
│  │  └─────────────────────────────────────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────┘ │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                Hardware Peripherals                     │ │
│  │  ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────────────┐ │ │
│  │  │   ADC   │ │  Timers │ │   PWM   │ │   CAN/Serial    │ │ │
│  │  └─────────┘ └─────────┘ └─────────┘ └─────────────────┘ │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 2. AKlippy Host Software Architecture

### 2.1 Core Framework

The host software is implemented in Python for rapid development and easy extensibility.

#### 2.1.1 Main Components

- **Reactor**: Event-driven main loop handling I/O and timing
- **Protocol Handler**: Manages communication with MCU
- **Configuration Manager**: Parses and validates configuration files
- **Module Manager**: Loads and manages automotive control modules
- **Logging System**: High-speed data acquisition and storage

#### 2.1.2 Control Modules

Each automotive function is implemented as a separate module:

```python
class EngineControlModule:
    def __init__(self, config):
        self.config = config
        self.mcu = None
        
    def setup(self, mcu):
        """Initialize module with MCU connection"""
        pass
        
    def update(self, sensors):
        """Process sensor data and generate control commands"""
        pass
        
    def get_status(self):
        """Return current module status"""
        pass
```

### 2.2 Communication Protocol

#### 2.2.1 Protocol Design

Based on Klipper's proven binary protocol with automotive-specific extensions:

- **Binary Encoding**: Compact VLQ (Variable Length Quantity) encoding
- **Command/Response**: Structured command-response pattern
- **Time Synchronization**: Precise timing coordination between host and MCU
- **Error Handling**: CRC checksums and automatic retransmission
- **Flow Control**: Windowed transmission for optimal bandwidth utilization

#### 2.2.2 Message Format

```
Message Block:
┌─────────┬─────────┬─────────────┬─────────┬─────────┐
│ Length  │Sequence │   Content   │   CRC   │  Sync   │
│ (1 byte)│(1 byte) │  (N bytes)  │(2 bytes)│(1 byte) │
└─────────┴─────────┴─────────────┴─────────┴─────────┘

Content Format:
┌─────────────┬─────────────┬─────────────┬─────────────┐
│ Command ID  │ Parameter 1 │ Parameter 2 │     ...     │
│   (VLQ)     │    (VLQ)    │    (VLQ)    │             │
└─────────────┴─────────────┴─────────────┴─────────────┘
```

#### 2.2.3 Automotive-Specific Commands

```c
// Fuel injection control
DECL_COMMAND(fuel_inject, "fuel_inject oid=%c duration=%u timing=%u");

// Ignition timing control  
DECL_COMMAND(ignition_fire, "ignition_fire oid=%c advance=%u dwell=%u");

// Sensor reading
DECL_COMMAND(read_sensors, "read_sensors mask=%u");

// Actuator control
DECL_COMMAND(set_actuator, "set_actuator oid=%c value=%u");
```

## 3. AMCU Firmware Architecture

### 3.1 Real-Time Operating System

The MCU firmware uses a lightweight RTOS or bare-metal approach optimized for automotive timing requirements:

- **Interrupt-Driven**: High-priority interrupts for critical timing
- **Task Scheduling**: Cooperative or preemptive scheduling for non-critical tasks
- **Memory Management**: Static allocation for predictable behavior
- **Timing Guarantees**: Deterministic response times for safety-critical functions

### 3.2 Hardware Abstraction Layer (HAL)

#### 3.2.1 Interface Definition

```c
// Generic sensor interface
typedef struct {
    uint8_t type;
    uint8_t channel;
    uint32_t (*read)(void);
    void (*configure)(uint32_t config);
} sensor_interface_t;

// Generic actuator interface
typedef struct {
    uint8_t type;
    uint8_t channel;
    void (*set_value)(uint32_t value);
    void (*set_timing)(uint32_t timing);
} actuator_interface_t;
```

#### 3.2.2 Supported Peripherals

- **ADC**: Multi-channel analog-to-digital conversion
- **Timers**: Precise timing for injection and ignition
- **PWM**: Pulse-width modulation for actuator control
- **CAN**: Controller Area Network communication
- **Serial**: UART communication with host
- **GPIO**: General-purpose I/O for sensors and actuators

### 3.3 Automotive-Specific Drivers

#### 3.3.1 Engine Position Sensors

```c
// Crankshaft position sensor
typedef struct {
    uint32_t teeth_count;
    uint32_t missing_teeth;
    uint32_t current_position;
    uint32_t rpm;
    void (*position_callback)(uint32_t position);
} crank_sensor_t;

// Camshaft position sensor
typedef struct {
    uint32_t cylinder_count;
    uint32_t current_cylinder;
    void (*sync_callback)(uint32_t cylinder);
} cam_sensor_t;
```

#### 3.3.2 Fuel Injection System

```c
// Fuel injector control
typedef struct {
    uint8_t injector_id;
    uint32_t flow_rate;      // cc/min
    uint32_t dead_time;      // microseconds
    uint32_t pulse_width;    // microseconds
    uint32_t timing_offset;  // degrees BTDC
} fuel_injector_t;
```

#### 3.3.3 Ignition System

```c
// Ignition coil control
typedef struct {
    uint8_t coil_id;
    uint32_t charge_time;    // microseconds
    uint32_t dwell_time;     // microseconds
    uint32_t timing_advance; // degrees BTDC
} ignition_coil_t;
```

## 4. Data Flow and Timing

### 4.1 Real-Time Constraints

| Function | Timing Requirement | Implementation |
|----------|-------------------|----------------|
| Fuel Injection | ±0.1° crank accuracy | MCU timer interrupt |
| Ignition Timing | ±0.1° crank accuracy | MCU timer interrupt |
| Sensor Reading | 1-10ms update rate | MCU ADC + DMA |
| CAN Communication | <5ms latency | MCU CAN controller |
| Host Communication | <10ms latency | Buffered protocol |

### 4.2 Control Loop Architecture

```
Sensor Input → MCU Processing → Host Algorithm → MCU Output → Actuator
     ↑                                                          ↓
     └─────────────── Feedback Loop ──────────────────────────┘

Timing:
- Sensor sampling: 1-10ms
- MCU processing: <100μs
- Host processing: 1-10ms
- Actuator response: <1ms
- Total loop time: <20ms
```

## 5. Safety and Fault Handling

### 5.1 Fault Detection

- **Sensor Validation**: Range checking and plausibility tests
- **Communication Monitoring**: Watchdog timers and heartbeat messages
- **Hardware Monitoring**: Supply voltage, temperature, and current monitoring
- **Software Monitoring**: Stack overflow detection and memory corruption checks

### 5.2 Safe States

- **Engine Shutdown**: Disable fuel and ignition on critical faults
- **Limp Mode**: Reduced functionality operation for non-critical faults
- **Fault Logging**: Persistent storage of fault codes and conditions
- **Recovery Procedures**: Automatic recovery from transient faults

## 6. Configuration and Calibration

### 6.1 Configuration File Format

```ini
# Engine configuration
[engine]
cylinders = 4
displacement = 2000  # cc
firing_order = 1,3,4,2
compression_ratio = 10.5

# Fuel system
[fuel]
injector_flow_rate = 440  # cc/min
fuel_pressure = 3.0       # bar
dead_time = 1.2          # ms

# Ignition system  
[ignition]
coil_charge_time = 3.0   # ms
dwell_time = 2.5         # ms
timing_offset = 10       # degrees BTDC
```

### 6.2 Runtime Calibration

- **Live Tuning**: Real-time parameter adjustment
- **Map-Based Control**: 3D lookup tables for fuel and ignition
- **Adaptive Learning**: Self-tuning algorithms for optimization
- **Data Logging**: High-speed acquisition for analysis and tuning

This architecture provides a solid foundation for building a flexible, reliable, and high-performance automotive ECU system while maintaining the proven benefits of Klipper's distributed approach.
