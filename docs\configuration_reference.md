# AKlippy Configuration Reference

This document provides a comprehensive reference for all AKlippy configuration options.

## Configuration File Format

AKlippy uses INI-style configuration files with sections and key-value pairs:

```ini
[section_name]
option_name: value
another_option: value

[another_section]
option: value
```

## Core Sections

### [mcu] - Microcontroller Configuration

The `[mcu]` section configures the connection to the automotive MCU.

```ini
[mcu]
serial: /dev/ttyUSB0    # Serial port for MCU communication
baud: 250000            # Baud rate (9600-2000000)
mcu_type: stm32f4       # MCU type (stm32f4, stm32f7)
```

**Options:**
- `serial` (required): Serial port device path
- `baud` (required): Communication baud rate
- `mcu_type` (optional): MCU type for hardware-specific optimizations

### [engine] - Engine Configuration

The `[engine]` section defines basic engine parameters.

```ini
[engine]
cylinders: 4                    # Number of cylinders (1-12)
displacement: 2000              # Engine displacement in cc
firing_order: 1,3,4,2          # Firing order sequence
compression_ratio: 10.5        # Compression ratio
redline_rpm: 7000              # Maximum safe RPM
```

**Options:**
- `cylinders` (required): Number of engine cylinders
- `displacement` (required): Engine displacement in cubic centimeters
- `firing_order` (optional): Cylinder firing order
- `compression_ratio` (optional): Engine compression ratio
- `redline_rpm` (optional): Maximum engine RPM

## Sensor Sections

### [crank_sensor] - Crankshaft Position Sensor

```ini
[crank_sensor]
pin: PA0                        # GPIO pin for sensor input
teeth: 60                       # Number of teeth on trigger wheel
missing_teeth: 2                # Number of missing teeth
trigger_edge: rising            # Trigger edge (rising/falling)
```

### [cam_sensor] - Camshaft Position Sensor

```ini
[cam_sensor]
pin: PA1                        # GPIO pin for sensor input
cylinders: 4                    # Number of cylinders for sync
trigger_edge: rising            # Trigger edge (rising/falling)
```

### [sensors] - Analog and Digital Sensors

```ini
[sensors]
# Temperature sensors
coolant_temp_pin: PA2           # Coolant temperature sensor
intake_air_temp_pin: PA3        # Intake air temperature sensor

# Pressure sensors
map_sensor_pin: PA4             # Manifold absolute pressure
oil_pressure_pin: PA5           # Oil pressure sensor
fuel_pressure_pin: PA6          # Fuel pressure sensor

# Position sensors
throttle_position_pin: PA7      # Throttle position sensor

# Oxygen sensor
o2_sensor_pin: PB4              # Oxygen sensor input
```

## Actuator Sections

### [fuel_system] - Fuel Injection System

```ini
[fuel_system]
# Injector configuration
injector_count: 4               # Number of fuel injectors
injector_pins: PB0,PB1,PB2,PB3  # GPIO pins for injectors
injector_flow_rate: 440         # Flow rate in cc/min at 3 bar
fuel_pressure: 3.0              # Fuel pressure in bar
dead_time: 1.2                  # Injector dead time in ms

# Fuel pump
fuel_pump_pin: PC0              # Fuel pump control pin
fuel_pump_prime_time: 2.0       # Pump prime time in seconds
```

### [ignition_system] - Ignition System

```ini
[ignition_system]
# Ignition coils
coil_count: 4                   # Number of ignition coils
coil_pins: PD0,PD1,PD2,PD3      # GPIO pins for coils
coil_charge_time: 3.0           # Coil charge time in ms
dwell_time: 2.5                 # Dwell time in ms
max_dwell_time: 8.0             # Maximum dwell time in ms

# Timing
base_timing: 10                 # Base timing in degrees BTDC
timing_offset: 0                # Timing offset in degrees
```

### [actuators] - Other Actuators

```ini
[actuators]
# Electronic throttle body
throttle_motor_pins: PC1,PC2    # Throttle motor control pins
throttle_position_sensor: PA7   # Throttle position feedback

# Idle air control
iac_pin: PC3                    # IAC valve control pin
iac_type: stepper               # IAC type (stepper/pwm)

# Variable valve timing
vvt_pin: PC4                    # VVT solenoid control pin
```

## Control Algorithm Sections

### [fuel_control] - Fuel Control Parameters

```ini
[fuel_control]
# Base fuel map (simplified example)
base_fuel_map: |
    # RPM:  1000  2000  3000  4000  5000  6000
    # Load:
    20:     8.5   9.0   9.5  10.0  10.5  11.0
    40:    12.5  13.0  13.5  14.0  14.5  15.0
    60:    16.5  17.0  17.5  18.0  18.5  19.0
    80:    20.5  21.0  21.5  22.0  22.5  23.0
    100:   24.5  25.0  25.5  26.0  26.5  27.0

# Acceleration enrichment
accel_enrich_threshold: 5.0     # %/s TPS change threshold
accel_enrich_amount: 20         # % fuel increase
accel_enrich_decay: 0.5         # Decay rate

# Closed loop control
enable_closed_loop: true        # Enable O2 feedback
target_afr: 14.7               # Target air-fuel ratio
o2_sensor_type: narrowband     # O2 sensor type
closed_loop_min_rpm: 800       # Minimum RPM for closed loop
closed_loop_max_rpm: 4000      # Maximum RPM for closed loop
```

### [ignition_control] - Ignition Control Parameters

```ini
[ignition_control]
# Base timing map (simplified example)
base_timing_map: |
    # RPM:  1000  2000  3000  4000  5000  6000
    # Load:
    20:     12    16    20    24    28    32
    40:     10    14    18    22    26    30
    60:      8    12    16    20    24    28
    80:      6    10    14    18    22    26
    100:     4     8    12    16    20    24

# Knock control
enable_knock_control: false     # Enable knock detection
knock_sensor_pin: PB5          # Knock sensor input pin
knock_retard_amount: 2.0       # Degrees to retard per knock
knock_recovery_rate: 0.1       # Recovery rate in degrees/cycle
```

### [idle_control] - Idle Speed Control

```ini
[idle_control]
target_idle_rpm: 800           # Target idle RPM
idle_control_type: iac         # Control type (iac/electronic_throttle)

# PID parameters
idle_p_gain: 0.5               # Proportional gain
idle_i_gain: 0.1               # Integral gain
idle_d_gain: 0.05              # Derivative gain
```

## Safety and Monitoring Sections

### [rev_limiter] - Rev Limiter

```ini
[rev_limiter]
soft_limit_rpm: 6500           # Soft limit RPM
hard_limit_rpm: 7000           # Hard limit RPM
limiter_type: fuel_cut         # Limiter type (fuel_cut/ignition_cut/both)
```

### [safety] - Safety Configuration

```ini
[safety]
# Failsafe settings
enable_failsafe: true          # Enable failsafe mode
failsafe_rpm_limit: 4000       # RPM limit in failsafe
failsafe_fuel_cut: true        # Cut fuel in failsafe
failsafe_ignition_retard: 10   # Retard ignition in failsafe

# Monitoring thresholds
max_coolant_temp: 110          # Maximum coolant temperature (°C)
max_oil_pressure: 7.0          # Maximum oil pressure (bar)
min_oil_pressure: 1.0          # Minimum oil pressure (bar)
max_intake_temp: 60            # Maximum intake temperature (°C)
```

### [diagnostics] - Diagnostic Configuration

```ini
[diagnostics]
enable_diagnostics: true       # Enable diagnostic functions
dtc_storage: eeprom           # DTC storage location
enable_obd2: false            # Enable OBD-II support
obd2_protocol: iso9141        # OBD-II protocol
```

## Data Logging and Communication

### [data_logging] - Data Logging

```ini
[data_logging]
enable_logging: true           # Enable data logging
log_rate: 100                 # Logging rate in Hz
log_channels: rpm,map,tps,coolant_temp,afr,timing_advance
log_file_path: /var/log/aklippy/
```

### [can_bus] - CAN Bus Configuration

```ini
[can_bus]
enable_can: true              # Enable CAN bus
can_interface: can0           # CAN interface name
can_bitrate: 500000          # CAN bitrate
can_filters: 0x100-0x1FF     # CAN ID filters
```

## Pin Naming Convention

AKlippy uses a standardized pin naming convention:

- **Format:** `P[PORT][PIN]`
- **Examples:** `PA0`, `PB15`, `PC7`
- **Ports:** A, B, C, D, E, F, G, H, I, J, K (depending on MCU)
- **Pins:** 0-15 (depending on port)

### STM32F4 Pin Mapping

Common pins for STM32F407:
- **PA0-PA15:** General purpose I/O, ADC inputs
- **PB0-PB15:** General purpose I/O, some with ADC
- **PC0-PC15:** General purpose I/O
- **PD0-PD15:** General purpose I/O

### Special Function Pins

Some pins have special functions:
- **ADC Inputs:** PA0-PA7, PB0-PB1, PC0-PC5
- **PWM Outputs:** Various timer channels
- **CAN:** PA11/PA12 (CAN1), PB5/PB6 (CAN2)
- **UART:** PA9/PA10 (USART1), PA2/PA3 (USART2)

## Value Types and Ranges

### Numeric Values

- **Integers:** Whole numbers (e.g., `4`, `1000`)
- **Floats:** Decimal numbers (e.g., `3.14`, `10.5`)
- **Ranges:** Specified in documentation for each option

### String Values

- **Pin names:** `PA0`, `PB15`
- **Choices:** `rising`, `falling`, `stm32f4`
- **Paths:** `/dev/ttyUSB0`, `/var/log/aklippy/`

### Lists

- **Comma-separated:** `1,3,4,2` or `PB0,PB1,PB2,PB3`
- **No spaces around commas**

### Multi-line Values

Use `|` for multi-line values like maps:

```ini
base_fuel_map: |
    # Comment line
    20:  8.5  9.0  9.5
    40: 12.5 13.0 13.5
```

## Configuration Validation

Always validate your configuration:

```bash
./tools/config_validator.py my_config.cfg
```

The validator checks:
- Required sections and options
- Value types and ranges
- Pin assignment conflicts
- Hardware compatibility

## Best Practices

1. **Start with examples:** Copy and modify example configurations
2. **Validate frequently:** Run the validator after each change
3. **Comment your changes:** Use `#` for comments
4. **Test incrementally:** Test each subsystem individually
5. **Keep backups:** Save working configurations
6. **Document modifications:** Note what you changed and why

## Troubleshooting Configuration

### Common Errors

**Missing required section:**
```
ERROR: Required section [mcu] is missing
```
Solution: Add the missing section with required options.

**Invalid pin format:**
```
ERROR: Invalid pin format: P0A. Expected format: P[A-K][0-15]
```
Solution: Use correct pin format (e.g., `PA0` not `P0A`).

**Pin conflict:**
```
ERROR: Pin PA0 is assigned to multiple functions
```
Solution: Each pin can only be used once.

**Value out of range:**
```
ERROR: Option 'cylinders' in [engine] is above maximum: 16 > 12
```
Solution: Use a value within the specified range.

This reference covers all major configuration options. For specific use cases, see the example configurations in the `configs/` directory.
