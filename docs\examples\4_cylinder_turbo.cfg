# AKlippy Configuration for 4-Cylinder Turbocharged Engine
# Example: Honda K20 Turbo Setup

# MCU Configuration
[mcu]
serial: /dev/ttyUSB0
baud: 250000
mcu_type: stm32f4

# Engine Configuration
[engine]
cylinders: 4
displacement: 2000  # cc (K20A)
firing_order: 1,3,4,2
compression_ratio: 8.5  # Lowered for turbo
redline_rpm: 8000

# Crankshaft Position Sensor (60-2 wheel)
[crank_sensor]
pin: PA0
teeth: 60
missing_teeth: 2
trigger_edge: rising

# Camshaft Position Sensor
[cam_sensor]
pin: PA1
cylinders: 4
trigger_edge: rising

# Fuel System Configuration
[fuel_system]
# High-flow injectors for turbo application
injector_count: 4
injector_pins: PB0,PB1,PB2,PB3
injector_flow_rate: 850  # cc/min at 3 bar (larger for turbo)
fuel_pressure: 4.0       # bar (higher pressure for turbo)
dead_time: 0.8          # ms (low impedance injectors)

# High-pressure fuel pump
fuel_pump_pin: PC0
fuel_pump_prime_time: 3.0  # seconds (longer for high pressure)

# Ignition System Configuration
[ignition_system]
# Individual coil-on-plug setup
coil_count: 4
coil_pins: PD0,PD1,PD2,PD3
coil_charge_time: 4.0   # ms (longer for high compression)
dwell_time: 3.0         # ms
max_dwell_time: 8.0     # ms

# Conservative base timing for turbo
base_timing: 8          # degrees BTDC (retarded for turbo)
timing_offset: 0        # degrees

# Sensor Configuration
[sensors]
# Temperature sensors
coolant_temp_pin: PA2
intake_air_temp_pin: PA3
exhaust_gas_temp_pin: PA8  # EGT sensor for turbo monitoring

# Pressure sensors
map_sensor_pin: PA4         # 3-bar MAP sensor for boost
oil_pressure_pin: PA5
fuel_pressure_pin: PA6
boost_pressure_pin: PA9     # Dedicated boost pressure sensor

# Position sensors
throttle_position_pin: PA7

# Oxygen sensors (wideband for tuning)
o2_sensor_pin: PB4
o2_sensor_type: wideband

# Turbo-specific sensors
turbo_speed_pin: PB5        # Turbo speed sensor (optional)

# Actuator Configuration
[actuators]
# Electronic throttle body
throttle_motor_pins: PC1,PC2
throttle_position_sensor: PA7

# Idle air control
iac_pin: PC3
iac_type: stepper

# Variable valve timing (VTEC)
vtec_pin: PC4
vtec_engagement_rpm: 5800

# Turbo control
wastegate_pin: PC5          # Electronic wastegate control
blow_off_valve_pin: PC6     # Blow-off valve control

# Engine Control Parameters
[fuel_control]
# Turbo fuel map with boost compensation
base_fuel_map: |
    # RPM:  1000  2000  3000  4000  5000  6000  7000  8000
    # Load (includes boost):
    20:     9.0   9.5  10.0  10.5  11.0  11.5  12.0  12.5
    40:    13.0  13.5  14.0  14.5  15.0  15.5  16.0  16.5
    60:    17.0  17.5  18.0  18.5  19.0  19.5  20.0  20.5
    80:    21.0  21.5  22.0  22.5  23.0  23.5  24.0  24.5
    100:   25.0  25.5  26.0  26.5  27.0  27.5  28.0  28.5
    120:   29.0  29.5  30.0  30.5  31.0  31.5  32.0  32.5  # Boost levels
    140:   33.0  33.5  34.0  34.5  35.0  35.5  36.0  36.5

# Boost-specific enrichment
boost_enrichment_threshold: 5.0  # psi above atmospheric
boost_enrichment_rate: 0.05      # 5% per psi of boost

# Acceleration enrichment (more aggressive for turbo)
accel_enrich_threshold: 3.0      # %/s TPS change
accel_enrich_amount: 25          # % fuel increase
accel_enrich_decay: 0.3          # Faster decay

# Closed loop control
enable_closed_loop: true
target_afr: 12.5                 # Richer for turbo (was 14.7 NA)
o2_sensor_type: wideband
closed_loop_min_rpm: 800
closed_loop_max_rpm: 6000       # Higher limit for turbo

# Boost-dependent AFR targets
boost_afr_map: |
    # Boost (psi): AFR target
    0:   14.7    # No boost - stoichiometric
    5:   13.5    # Light boost
    10:  12.5    # Medium boost
    15:  11.8    # High boost
    20:  11.5    # Maximum boost

[ignition_control]
# Conservative timing map for turbo application
base_timing_map: |
    # RPM:  1000  2000  3000  4000  5000  6000  7000  8000
    # Load (includes boost):
    20:     15    18    22    25    28    30    32    34
    40:     12    15    18    21    24    26    28    30
    60:      8    11    14    17    20    22    24    26
    80:      5     8    11    14    17    19    21    23
    100:     2     5     8    11    14    16    18    20
    120:    -2     1     4     7    10    12    14    16  # Boost timing
    140:    -5    -2     1     4     7     9    11    13  # High boost

# Knock control (critical for turbo)
enable_knock_control: true
knock_sensor_pin: PB5
knock_retard_amount: 3.0         # More aggressive retard
knock_recovery_rate: 0.05        # Slower recovery
max_knock_retard: 15.0           # Maximum total retard

# Boost-dependent timing retard
boost_timing_retard: |
    # Boost (psi): Timing retard (degrees)
    0:   0      # No boost - no retard
    5:   2      # Light boost
    10:  4      # Medium boost
    15:  6      # High boost
    20:  8      # Maximum boost

[boost_control]
# Wastegate control
enable_boost_control: true
target_boost_map: |
    # RPM:  2000  3000  4000  5000  6000  7000  8000
    # TPS:
    50:     3     5     8    10    12    14    15    # psi
    75:     5     8    12    15    18    20    20
    100:    8    12    16    20    22    22    22

# Wastegate PID parameters
boost_p_gain: 0.8
boost_i_gain: 0.2
boost_d_gain: 0.1

# Safety limits
max_boost_pressure: 22.0         # psi (absolute maximum)
boost_cut_threshold: 24.0        # psi (emergency cut)

# Overboost protection
overboost_time_limit: 2.0        # seconds
overboost_recovery_time: 10.0    # seconds

[idle_control]
target_idle_rpm: 900             # Slightly higher for turbo
idle_control_type: iac

# More aggressive PID for turbo idle stability
idle_p_gain: 0.8
idle_i_gain: 0.15
idle_d_gain: 0.08

[rev_limiter]
soft_limit_rpm: 7800             # Soft limit
hard_limit_rpm: 8200             # Hard limit (above redline for safety)
limiter_type: fuel_cut           # Fuel cut for turbo protection

[safety]
# Enhanced safety for turbo application
enable_failsafe: true
failsafe_rpm_limit: 3000         # Lower limit for turbo safety
failsafe_fuel_cut: true
failsafe_ignition_retard: 15     # More retard for safety

# Turbo-specific monitoring thresholds
max_coolant_temp: 105            # °C (lower for turbo)
max_oil_pressure: 8.0            # bar
min_oil_pressure: 2.0            # bar (higher minimum)
max_intake_temp: 50              # °C (charge air temp)
max_exhaust_temp: 950            # °C (EGT limit)
max_boost_pressure: 22.0         # psi

# Overheating protection
enable_overheat_protection: true
overheat_fuel_cut_temp: 110      # °C
overheat_boost_cut_temp: 105     # °C

[vtec_control]
# Honda VTEC control
enable_vtec: true
vtec_engagement_rpm: 5800        # RPM
vtec_disengagement_rpm: 5600     # RPM (hysteresis)
vtec_min_oil_pressure: 3.0       # bar (safety requirement)
vtec_min_coolant_temp: 70        # °C (warm-up requirement)

[launch_control]
# Launch control for turbo applications
enable_launch_control: true
launch_rpm_limit: 4000           # RPM
launch_boost_limit: 15.0         # psi
launch_timing_retard: 10         # degrees
launch_activation_speed: 5       # km/h (disable above this speed)

[data_logging]
enable_logging: true
log_rate: 200                    # Hz (higher rate for turbo)
log_channels: rpm,map,boost,tps,coolant_temp,intake_temp,egt,afr,timing_advance,knock_count,vtec_active
log_file_path: /var/log/aklippy/turbo/

[diagnostics]
enable_diagnostics: true
dtc_storage: eeprom
enable_obd2: true
obd2_protocol: can

# Turbo-specific diagnostic codes
custom_dtc_codes: |
    P1000: Overboost condition
    P1001: Charge air temperature too high
    P1002: Exhaust gas temperature too high
    P1003: Turbo speed sensor fault
    P1004: Wastegate control fault
