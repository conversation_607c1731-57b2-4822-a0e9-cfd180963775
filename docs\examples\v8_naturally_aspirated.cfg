# AKlippy Configuration for V8 Naturally Aspirated Engine
# Example: LS3 6.2L V8 Setup

# MCU Configuration
[mcu]
serial: /dev/ttyUSB0
baud: 250000
mcu_type: stm32f7  # F7 for more I/O and processing power

# Engine Configuration
[engine]
cylinders: 8
displacement: 6200  # cc (LS3)
firing_order: 1,8,7,2,6,5,4,3  # LS firing order
compression_ratio: 10.7
redline_rpm: 6600

# Crankshaft Position Sensor (58x wheel)
[crank_sensor]
pin: PA0
teeth: 58
missing_teeth: 2
trigger_edge: rising

# Camshaft Position Sensor
[cam_sensor]
pin: PA1
cylinders: 8
trigger_edge: rising

# Fuel System Configuration
[fuel_system]
# Sequential injection for all 8 cylinders
injector_count: 8
injector_pins: PB0,PB1,PB2,PB3,PB4,PB5,PB6,PB7
injector_flow_rate: 650  # cc/min at 3 bar
fuel_pressure: 3.5       # bar
dead_time: 1.0          # ms

# Fuel pump
fuel_pump_pin: PC0
fuel_pump_prime_time: 2.0

# Ignition System Configuration
[ignition_system]
# Coil-near-plug setup (8 coils)
coil_count: 8
coil_pins: PD0,PD1,PD2,PD3,PD4,PD5,PD6,PD7
coil_charge_time: 3.5   # ms
dwell_time: 2.8         # ms
max_dwell_time: 8.0     # ms

# Base timing for naturally aspirated
base_timing: 15         # degrees BTDC
timing_offset: 0        # degrees

# Sensor Configuration
[sensors]
# Temperature sensors
coolant_temp_pin: PA2
intake_air_temp_pin: PA3
oil_temp_pin: PA8       # Oil temperature

# Pressure sensors
map_sensor_pin: PA4     # 1-bar MAP sensor (NA application)
oil_pressure_pin: PA5
fuel_pressure_pin: PA6

# Position sensors
throttle_position_pin: PA7
pedal_position_pin: PA9  # Accelerator pedal position

# Mass airflow sensor
maf_sensor_pin: PA10

# Oxygen sensors (dual bank)
o2_sensor_bank1_pin: PB8   # Bank 1 (cylinders 1,3,5,7)
o2_sensor_bank2_pin: PB9   # Bank 2 (cylinders 2,4,6,8)

# Knock sensors (dual)
knock_sensor_bank1_pin: PB10
knock_sensor_bank2_pin: PB11

# Actuator Configuration
[actuators]
# Electronic throttle body (drive-by-wire)
throttle_motor_pins: PC1,PC2
throttle_position_sensor: PA7

# Idle air control
iac_pin: PC3
iac_type: stepper

# Variable valve timing (dual cam phasers)
vvt_intake_pin: PC4     # Intake cam phaser
vvt_exhaust_pin: PC5    # Exhaust cam phaser

# Cooling fan control
cooling_fan_pin: PC6
cooling_fan_type: pwm

# A/C compressor clutch
ac_clutch_pin: PC7

# Engine Control Parameters
[fuel_control]
# Naturally aspirated fuel map
base_fuel_map: |
    # RPM:   800  1500  2000  2500  3000  3500  4000  4500  5000  5500  6000  6500
    # Load:
    10:      6.0   6.5   7.0   7.5   8.0   8.5   9.0   9.5  10.0  10.5  11.0  11.5
    20:      8.0   8.5   9.0   9.5  10.0  10.5  11.0  11.5  12.0  12.5  13.0  13.5
    30:     10.0  10.5  11.0  11.5  12.0  12.5  13.0  13.5  14.0  14.5  15.0  15.5
    40:     12.0  12.5  13.0  13.5  14.0  14.5  15.0  15.5  16.0  16.5  17.0  17.5
    50:     14.0  14.5  15.0  15.5  16.0  16.5  17.0  17.5  18.0  18.5  19.0  19.5
    60:     16.0  16.5  17.0  17.5  18.0  18.5  19.0  19.5  20.0  20.5  21.0  21.5
    70:     18.0  18.5  19.0  19.5  20.0  20.5  21.0  21.5  22.0  22.5  23.0  23.5
    80:     20.0  20.5  21.0  21.5  22.0  22.5  23.0  23.5  24.0  24.5  25.0  25.5
    90:     22.0  22.5  23.0  23.5  24.0  24.5  25.0  25.5  26.0  26.5  27.0  27.5
    100:    24.0  24.5  25.0  25.5  26.0  26.5  27.0  27.5  28.0  28.5  29.0  29.5

# Power enrichment for WOT
power_enrichment_threshold: 85   # % TPS
power_enrichment_afr: 12.8      # Target AFR for power

# Acceleration enrichment
accel_enrich_threshold: 4.0     # %/s TPS change
accel_enrich_amount: 15         # % fuel increase
accel_enrich_decay: 0.5         # Decay rate

# Closed loop control (dual bank)
enable_closed_loop: true
target_afr: 14.7               # Stoichiometric
o2_sensor_type: narrowband
closed_loop_min_rpm: 600
closed_loop_max_rpm: 4500

# Bank-to-bank fuel trim
enable_bank_trim: true
max_bank_trim: 25              # % maximum trim

[ignition_control]
# Naturally aspirated timing map
base_timing_map: |
    # RPM:   800  1500  2000  2500  3000  3500  4000  4500  5000  5500  6000  6500
    # Load:
    10:      12    15    18    21    24    27    30    33    36    38    40    42
    20:      10    13    16    19    22    25    28    31    34    36    38    40
    30:       8    11    14    17    20    23    26    29    32    34    36    38
    40:       6     9    12    15    18    21    24    27    30    32    34    36
    50:       4     7    10    13    16    19    22    25    28    30    32    34
    60:       2     5     8    11    14    17    20    23    26    28    30    32
    70:       0     3     6     9    12    15    18    21    24    26    28    30
    80:      -2     1     4     7    10    13    16    19    22    24    26    28
    90:      -4    -1     2     5     8    11    14    17    20    22    24    26
    100:     -6    -3     0     3     6     9    12    15    18    20    22    24

# Knock control (dual bank)
enable_knock_control: true
knock_sensor_bank1_pin: PB10
knock_sensor_bank2_pin: PB11
knock_retard_amount: 2.0       # Degrees per knock event
knock_recovery_rate: 0.1       # Recovery rate
max_knock_retard: 12.0         # Maximum total retard

# Individual cylinder knock control
enable_cylinder_knock_control: true

[variable_valve_timing]
# Dual cam phaser control
enable_vvt: true

# Intake cam timing
intake_vvt_pin: PC4
intake_vvt_advance_map: |
    # RPM:   800  1500  2500  3500  4500  5500  6500
    # Load:
    20:       0     5    10    15    20    25    30
    40:       0     8    15    22    28    32    35
    60:       0    10    18    25    30    35    38
    80:       0    12    20    28    32    38    40
    100:      0    15    22    30    35    40    42

# Exhaust cam timing
exhaust_vvt_pin: PC5
exhaust_vvt_retard_map: |
    # RPM:   800  1500  2500  3500  4500  5500  6500
    # Load:
    20:       0     2     5     8    12    15    18
    40:       0     3     8    12    16    20    22
    60:       0     5    10    15    20    24    26
    80:       0     8    12    18    22    26    28
    100:      0    10    15    20    25    28    30

[idle_control]
target_idle_rpm: 650           # Low idle for V8
idle_control_type: iac

# PID parameters
idle_p_gain: 0.6
idle_i_gain: 0.12
idle_d_gain: 0.06

# Idle timing control
enable_idle_timing_control: true
idle_timing_range: 10          # Degrees of timing control

[rev_limiter]
soft_limit_rpm: 6400
hard_limit_rpm: 6800
limiter_type: fuel_cut

[cooling_control]
# Cooling fan control
enable_fan_control: true
fan_on_temp: 95                # °C
fan_off_temp: 88               # °C (hysteresis)
fan_pin: PC6

# A/C control
enable_ac_control: true
ac_clutch_pin: PC7
ac_cutout_rpm: 6000            # Disable A/C at high RPM
ac_cutout_load: 90             # Disable A/C at high load

[transmission_control]
# Automatic transmission control (if equipped)
enable_trans_control: false
trans_temp_pin: PB12
torque_converter_lockup_pin: PC8

[safety]
enable_failsafe: true
failsafe_rpm_limit: 4000
failsafe_fuel_cut: true
failsafe_ignition_retard: 10

# V8-specific monitoring
max_coolant_temp: 115          # °C
max_oil_pressure: 7.0          # bar
min_oil_pressure: 1.5          # bar
max_intake_temp: 65            # °C
max_oil_temp: 130              # °C

# Cylinder deactivation safety (if equipped)
enable_cylinder_deactivation_safety: false

[data_logging]
enable_logging: true
log_rate: 100                  # Hz
log_channels: rpm,map,maf,tps,coolant_temp,oil_temp,afr_bank1,afr_bank2,timing_advance,knock_count_bank1,knock_count_bank2,vvt_intake,vvt_exhaust
log_file_path: /var/log/aklippy/v8/

[diagnostics]
enable_diagnostics: true
dtc_storage: eeprom
enable_obd2: true
obd2_protocol: can

# V8-specific diagnostic codes
custom_dtc_codes: |
    P1100: Bank 1 fuel trim out of range
    P1101: Bank 2 fuel trim out of range
    P1102: Intake VVT system fault
    P1103: Exhaust VVT system fault
    P1104: Knock sensor bank 1 fault
    P1105: Knock sensor bank 2 fault
    P1106: MAF sensor out of range
    P1107: Cooling fan control fault
