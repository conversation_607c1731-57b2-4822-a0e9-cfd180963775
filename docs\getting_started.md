# Getting Started with <PERSON><PERSON><PERSON>

## Introduction

AKlippy (Automotive Klipper) is an open-source ECU firmware framework that brings the proven distributed architecture of K<PERSON>per 3D printer firmware to automotive applications. This guide will help you get started with <PERSON><PERSON><PERSON>, from installation to running your first engine control system.

## What You'll Need

### Hardware Requirements

**Minimum Setup:**
- Host computer (Raspberry Pi 4 or equivalent)
- Automotive-grade MCU development board (STM32F4 or STM32F7)
- USB cable for MCU programming
- Serial/CAN interface for communication

**Recommended Setup:**
- Raspberry Pi 4 (4GB RAM) or automotive SBC
- STM32F407 or STM32F767 development board
- ST-Link programmer
- CAN transceiver module (MCP2515 or similar)
- Breadboard and jumper wires for prototyping

**For Real Engine Testing:**
- Automotive-grade ECU hardware
- Engine position sensors (crank/cam)
- Fuel injectors and drivers
- Ignition coils and drivers
- Various automotive sensors (MAP, TPS, temperature, etc.)

### Software Requirements

- Linux-based OS (Ubuntu 20.04+ recommended)
- Python 3.8 or later
- ARM GCC toolchain
- Git

## Installation

### Quick Installation (Recommended)

The easiest way to install <PERSON><PERSON><PERSON> is using our installation script:

```bash
# Download and run the installation script
curl -sSL https://raw.githubusercontent.com/your-org/aklippy/main/scripts/install.sh | bash
```

This script will:
- Install system dependencies
- Create a dedicated AKlippy user
- Clone the repository
- Set up Python virtual environment
- Configure systemd service
- Set up hardware access permissions

### Manual Installation

If you prefer to install manually:

1. **Install system dependencies:**
```bash
sudo apt update
sudo apt install -y python3 python3-pip python3-venv git build-essential \
    gcc-arm-none-eabi stlink-tools can-utils
```

2. **Clone the repository:**
```bash
git clone https://github.com/your-org/aklippy.git
cd aklippy
```

3. **Create Python virtual environment:**
```bash
python3 -m venv aklippy-env
source aklippy-env/bin/activate
pip install -r requirements.txt
```

4. **Make scripts executable:**
```bash
chmod +x aklippy_main.py tools/*.py scripts/*.sh
```

## Configuration

### Basic Configuration

AKlippy uses human-readable configuration files similar to Klipper. Start with the example configuration:

```bash
cp configs/example_basic.cfg my_config.cfg
```

Edit the configuration file to match your hardware:

```ini
# MCU Configuration
[mcu]
serial: /dev/ttyUSB0  # Adjust to your MCU connection
baud: 250000
mcu_type: stm32f4

# Engine Configuration
[engine]
cylinders: 4
displacement: 2000  # cc
firing_order: 1,3,4,2
compression_ratio: 10.5

# Sensor Configuration
[sensors]
coolant_temp_pin: PA2
map_sensor_pin: PA4
throttle_position_pin: PA7

# Fuel System
[fuel_system]
injector_pins: PB0,PB1,PB2,PB3
injector_flow_rate: 440  # cc/min
fuel_pressure: 3.0       # bar
```

### Configuration Validation

Always validate your configuration before use:

```bash
./tools/config_validator.py my_config.cfg
```

This will check for:
- Required sections and options
- Value ranges and types
- Pin assignment conflicts
- Hardware compatibility

## Building and Flashing Firmware

### Build MCU Firmware

Build the AMCU firmware for your target MCU:

```bash
# Build for STM32F4
./tools/build_firmware.py build -t stm32f4 -c my_config.cfg

# Build for STM32F7
./tools/build_firmware.py build -t stm32f7 -c my_config.cfg
```

### Flash Firmware

Connect your MCU via ST-Link and flash the firmware:

```bash
# Flash using st-flash (default)
./tools/build_firmware.py flash -t stm32f4

# Flash using OpenOCD
./tools/build_firmware.py flash -t stm32f4 -m openocd

# Flash using DFU
./tools/build_firmware.py flash -t stm32f4 -m dfu
```

## Running AKlippy

### Test Run

Start AKlippy manually for testing:

```bash
./aklippy_main.py my_config.cfg -v
```

You should see output similar to:
```
2024-01-01 12:00:00 - aklippy - INFO - Starting AKlippy v0.1.0
2024-01-01 12:00:00 - config - INFO - Configuration loaded from: my_config.cfg
2024-01-01 12:00:00 - mcu - INFO - MCU configured: /dev/ttyUSB0 at 250000 baud
2024-01-01 12:00:00 - mcu - INFO - MCU connected successfully
2024-01-01 12:00:00 - aklippy - INFO - AKlippy ready for operation
```

### Service Installation

For production use, install as a systemd service:

```bash
# Copy service file
sudo cp scripts/aklippy.service /etc/systemd/system/
sudo systemctl daemon-reload

# Start and enable service
sudo systemctl start aklippy
sudo systemctl enable aklippy

# Check status
sudo systemctl status aklippy
```

## Basic Testing

### Sensor Reading Test

Test sensor readings:

```bash
# In another terminal, monitor the log
sudo journalctl -u aklippy -f
```

You should see periodic sensor readings in the log.

### Actuator Test

Test actuator outputs (ensure no engine is connected):

1. Configure test outputs in your config
2. Use the built-in test commands
3. Verify outputs with an oscilloscope or LED

### Communication Test

Test MCU communication:

```bash
# Check MCU status
echo "get_status" | nc localhost 7125  # If API server is enabled
```

## Safety Considerations

⚠️ **IMPORTANT SAFETY WARNINGS** ⚠️

- **Never connect AKlippy to a running engine without proper testing**
- **Always use appropriate safety equipment and procedures**
- **Test all functions thoroughly on a bench setup first**
- **Ensure proper grounding and electrical isolation**
- **Have emergency stop procedures in place**
- **Follow all automotive safety standards**

### Pre-Engine Testing Checklist

Before connecting to a real engine:

- [ ] Configuration validated with no errors
- [ ] All sensors reading reasonable values
- [ ] All actuators responding correctly
- [ ] Emergency stop function tested
- [ ] Failsafe modes verified
- [ ] Communication stability confirmed
- [ ] Timing accuracy verified with oscilloscope

## Troubleshooting

### Common Issues

**MCU Connection Failed:**
- Check USB cable and connections
- Verify correct serial port in config
- Ensure MCU is in bootloader mode if needed
- Check permissions: `sudo usermod -a -G dialout $USER`

**Configuration Errors:**
- Run config validator: `./tools/config_validator.py my_config.cfg`
- Check for typos in pin names
- Verify value ranges are correct

**Build Errors:**
- Install ARM GCC toolchain: `sudo apt install gcc-arm-none-eabi`
- Check target MCU is supported
- Verify all dependencies are installed

**Permission Denied:**
- Add user to dialout group: `sudo usermod -a -G dialout $USER`
- Log out and back in for changes to take effect
- Check udev rules are installed

### Getting Help

- Check the [documentation](docs/)
- Search [GitHub issues](https://github.com/your-org/aklippy/issues)
- Join the [community forum](https://forum.aklippy.org)
- Read the [FAQ](docs/faq.md)

## Next Steps

Once you have AKlippy running:

1. **Learn the Configuration System:** Read [Configuration Reference](configuration_reference.md)
2. **Understand the Architecture:** Study [Architecture Guide](architecture.md)
3. **Explore Examples:** Check out [Example Configurations](examples/)
4. **Join the Community:** Participate in development and discussions
5. **Contribute:** Help improve AKlippy for everyone

## Quick Reference

### Essential Commands

```bash
# Validate configuration
./tools/config_validator.py config.cfg

# Build firmware
./tools/build_firmware.py build -t stm32f4 -c config.cfg

# Flash firmware
./tools/build_firmware.py flash -t stm32f4

# Run AKlippy
./aklippy_main.py config.cfg

# Check service status
sudo systemctl status aklippy

# View logs
sudo journalctl -u aklippy -f
```

### Important Files

- `configs/example_basic.cfg` - Example configuration
- `docs/` - Documentation directory
- `tools/build_firmware.py` - Firmware build tool
- `tools/config_validator.py` - Configuration validator
- `aklippy_main.py` - Main AKlippy application

Welcome to the AKlippy community! We're excited to see what you'll build with this powerful automotive ECU framework.
