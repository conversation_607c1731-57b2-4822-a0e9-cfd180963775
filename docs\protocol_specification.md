# AKlippy Communication Protocol Specification

## 1. Overview

The AKlippy communication protocol is based on <PERSON><PERSON><PERSON>'s proven binary protocol, extended with automotive-specific commands and optimized for real-time automotive control applications.

## 2. Protocol Layers

### 2.1 Physical Layer
- **Serial**: UART at 250kbps (default) or 1Mbps
- **CAN**: ISO 11898 standard at 500kbps or 1Mbps
- **USB**: Virtual COM port for development
- **Ethernet**: TCP/IP for high-bandwidth applications (future)

### 2.2 Data Link Layer
- **Framing**: HDLC-inspired message blocks
- **Error Detection**: 16-bit CRC-CCITT
- **Flow Control**: Windowed transmission with ACK/NAK
- **Sequence Numbers**: 4-bit sequence counter

### 2.3 Application Layer
- **Command/Response**: RPC-style communication
- **Data Dictionary**: Dynamic command/response definitions
- **Time Synchronization**: Host-MCU clock coordination
- **Streaming Data**: High-frequency sensor data streams

## 3. Message Format

### 3.1 Message Block Structure

```
┌─────────┬─────────┬─────────────┬─────────┬─────────┐
│ Length  │Sequence │   Content   │   CRC   │  Sync   │
│ (1 byte)│(1 byte) │  (N bytes)  │(2 bytes)│(1 byte) │
└─────────┴─────────┴─────────────┴─────────┴─────────┘

Length:   Total message length including header and trailer (5-64 bytes)
Sequence: 4-bit sequence number + 4-bit flags (0x10 for normal messages)
Content:  Variable length command/response data
CRC:      16-bit CRC-CCITT of header + content
Sync:     Synchronization byte (0x7E)
```

### 3.2 Content Encoding

Commands and responses are encoded as Variable Length Quantities (VLQ):

```
┌─────────────┬─────────────┬─────────────┬─────────────┐
│ Command ID  │ Parameter 1 │ Parameter 2 │     ...     │
│   (VLQ)     │    (VLQ)    │    (VLQ)    │             │
└─────────────┴─────────────┴─────────────┴─────────────┘
```

### 3.3 Variable Length Quantity (VLQ) Encoding

| Integer Range | Encoded Bytes | Example |
|---------------|---------------|---------|
| -32 to 95 | 1 | 0x40 = 0 |
| -4096 to 12287 | 2 | 0x81 0x00 = 128 |
| -524288 to 1572863 | 3 | 0x82 0x80 0x00 = 16384 |

## 4. Command Categories

### 4.1 System Commands

#### 4.1.1 Identification and Setup
```c
// Get MCU identification and capabilities
DECL_COMMAND(identify, "identify offset=%u count=%c");
DECL_RESPONSE(identify_response, "identify_response offset=%u data=%*s");

// Emergency stop
DECL_COMMAND(emergency_stop, "emergency_stop");

// Reset MCU
DECL_COMMAND(reset, "reset");

// Get configuration
DECL_COMMAND(get_config, "get_config");
DECL_RESPONSE(config, "config is_config=%c crc=%u move_count=%hu is_shutdown=%c");
```

#### 4.1.2 Time Synchronization
```c
// Get MCU clock
DECL_COMMAND(get_clock, "get_clock");
DECL_RESPONSE(clock, "clock clock=%u");

// Set MCU clock
DECL_COMMAND(set_clock, "set_clock clock=%u");
```

### 4.2 Automotive-Specific Commands

#### 4.2.1 Engine Position Sensors
```c
// Configure crankshaft position sensor
DECL_COMMAND(config_crank_sensor, 
    "config_crank_sensor oid=%c pin=%u teeth=%u missing=%u");

// Configure camshaft position sensor  
DECL_COMMAND(config_cam_sensor,
    "config_cam_sensor oid=%c pin=%u cylinders=%u");

// Engine position response
DECL_RESPONSE(engine_position,
    "engine_position timestamp=%u crank_pos=%u cam_pos=%c rpm=%u");
```

#### 4.2.2 Fuel Injection Control
```c
// Configure fuel injector
DECL_COMMAND(config_injector,
    "config_injector oid=%c pin=%u flow_rate=%u dead_time=%u");

// Schedule fuel injection
DECL_COMMAND(schedule_injection,
    "schedule_injection oid=%c clock=%u duration=%u");

// Batch injection (multiple cylinders)
DECL_COMMAND(batch_injection,
    "batch_injection mask=%c clock=%u duration=%u");

// Sequential injection
DECL_COMMAND(sequential_injection,
    "sequential_injection oid=%c crank_pos=%u duration=%u offset=%u");
```

#### 4.2.3 Ignition Control
```c
// Configure ignition coil
DECL_COMMAND(config_ignition,
    "config_ignition oid=%c pin=%u charge_time=%u max_dwell=%u");

// Schedule ignition event
DECL_COMMAND(schedule_ignition,
    "schedule_ignition oid=%c clock=%u dwell=%u");

// Set ignition timing
DECL_COMMAND(set_ignition_timing,
    "set_ignition_timing oid=%c advance=%u dwell=%u");
```

#### 4.2.4 Sensor Reading
```c
// Configure analog sensor
DECL_COMMAND(config_analog_sensor,
    "config_analog_sensor oid=%c pin=%c sample_time=%u sample_count=%c");

// Configure digital sensor
DECL_COMMAND(config_digital_sensor,
    "config_digital_sensor oid=%c pin=%u pull_up=%c");

// Query sensor values
DECL_COMMAND(query_sensors, "query_sensors oid=%c clock=%u rest_ticks=%u");

// Sensor data response
DECL_RESPONSE(sensor_data,
    "sensor_data oid=%c timestamp=%u value=%u");

// Batch sensor reading
DECL_RESPONSE(sensor_batch,
    "sensor_batch timestamp=%u sensors=%*s");
```

#### 4.2.5 Actuator Control
```c
// Configure PWM output
DECL_COMMAND(config_pwm,
    "config_pwm oid=%c pin=%u frequency=%u");

// Set PWM duty cycle
DECL_COMMAND(set_pwm,
    "set_pwm oid=%c clock=%u duty=%u");

// Configure stepper motor (for electronic throttle)
DECL_COMMAND(config_stepper,
    "config_stepper oid=%c step_pin=%u dir_pin=%u enable_pin=%u");

// Move stepper
DECL_COMMAND(queue_step,
    "queue_step oid=%c interval=%u count=%hu add=%hi");
```

#### 4.2.6 CAN Bus Interface
```c
// Configure CAN interface
DECL_COMMAND(config_can,
    "config_can oid=%c bitrate=%u filters=%*s");

// Send CAN message
DECL_COMMAND(can_send,
    "can_send oid=%c id=%u data=%*s");

// CAN message received
DECL_RESPONSE(can_receive,
    "can_receive oid=%c timestamp=%u id=%u data=%*s");
```

### 4.3 Diagnostic Commands

#### 4.3.1 Status and Monitoring
```c
// Get system status
DECL_COMMAND(get_status, "get_status");
DECL_RESPONSE(status,
    "status clock=%u status=%c uptime=%u temp=%u voltage=%u");

// Get fault codes
DECL_COMMAND(get_faults, "get_faults");
DECL_RESPONSE(fault_codes,
    "fault_codes count=%c codes=%*s");

// Clear fault codes
DECL_COMMAND(clear_faults, "clear_faults");
```

#### 4.3.2 Data Logging
```c
// Start data logging
DECL_COMMAND(start_logging,
    "start_logging oid=%c rate=%u channels=%*s");

// Stop data logging
DECL_COMMAND(stop_logging, "stop_logging oid=%c");

// Log data response
DECL_RESPONSE(log_data,
    "log_data timestamp=%u data=%*s");
```

## 5. Protocol Flow

### 5.1 Connection Establishment

```
Host                           MCU
 │                              │
 ├─── identify ────────────────→│
 │←─── identify_response ──────┤
 ├─── get_config ─────────────→│
 │←─── config ─────────────────┤
 ├─── get_clock ──────────────→│
 │←─── clock ──────────────────┤
 │                              │
 │    Configuration Phase       │
 ├─── config_* commands ──────→│
 │←─── ack ────────────────────┤
 │                              │
 │    Operational Phase         │
 ├─── control commands ───────→│
 │←─── sensor responses ───────┤
```

### 5.2 Real-Time Operation

```
Host                           MCU
 │                              │
 │    Engine Control Loop       │
 ├─── schedule_injection ─────→│
 ├─── schedule_ignition ──────→│
 ├─── set_pwm ────────────────→│
 │←─── engine_position ────────┤
 │←─── sensor_data ────────────┤
 │                              │
 │    Continuous Operation      │
 ├─── query_sensors ──────────→│
 │←─── sensor_batch ───────────┤
 ├─── batch_injection ────────→│
 │←─── status ─────────────────┤
```

### 5.3 Error Handling

```
Host                           MCU
 │                              │
 ├─── command ────────────────→│
 │                              │ (CRC error detected)
 │←─── nak ────────────────────┤
 ├─── command (retransmit) ───→│
 │←─── ack ────────────────────┤
 │                              │
 │    Fault Condition           │
 │←─── fault_codes ────────────┤
 ├─── emergency_stop ─────────→│
 │←─── ack ────────────────────┤
```

## 6. Timing Requirements

### 6.1 Command Response Times

| Command Type | Max Response Time | Notes |
|--------------|-------------------|-------|
| System Commands | 10ms | Non-critical |
| Sensor Queries | 5ms | Real-time data |
| Control Commands | 1ms | Critical timing |
| Emergency Stop | 100μs | Safety critical |

### 6.2 Data Streaming Rates

| Data Type | Update Rate | Bandwidth |
|-----------|-------------|-----------|
| Engine Position | 1000 Hz | 8 kbps |
| Sensor Data | 100 Hz | 16 kbps |
| Status Updates | 10 Hz | 2 kbps |
| Log Data | Variable | Up to 100 kbps |

## 7. Security Considerations

### 7.1 Authentication
- Optional message authentication codes (MAC)
- Sequence number validation
- Timestamp verification

### 7.2 Access Control
- Command privilege levels
- Read-only vs. read-write access
- Emergency override capabilities

This protocol specification provides a robust foundation for real-time automotive control while maintaining compatibility with Klipper's proven communication architecture.
