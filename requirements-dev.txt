# AKlippy Development Dependencies

# Testing
pytest>=6.0
pytest-cov>=2.0
pytest-mock>=3.0
pytest-asyncio>=0.18

# Code formatting and linting
black>=21.0
flake8>=3.9
isort>=5.0
mypy>=0.910

# Pre-commit hooks
pre-commit>=2.15

# Documentation
sphinx>=4.0
sphinx-rtd-theme>=1.0
myst-parser>=0.17

# Development tools
ipython>=7.0
jupyter>=1.0

# Debugging
pdb++>=0.10

# Performance profiling
line_profiler>=3.0
memory_profiler>=0.60

# Hardware simulation for testing
can-isotp>=1.7
python-can>=4.0

# Additional testing utilities
factory-boy>=3.2
freezegun>=1.2
