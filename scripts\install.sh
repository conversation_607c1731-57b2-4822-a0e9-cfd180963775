#!/bin/bash
# AKlippy Installation Script
# This script installs AKlippy and its dependencies

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
AKLIPPY_USER="aklippy"
AKLIPPY_HOME="/home/<USER>"
AKLIPPY_DIR="$AKLIPPY_HOME/aklippy"
SERVICE_NAME="aklippy"

# Functions
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_root() {
    if [[ $EUID -eq 0 ]]; then
        print_error "This script should not be run as root"
        exit 1
    fi
}

check_os() {
    if [[ ! -f /etc/os-release ]]; then
        print_error "Cannot determine OS version"
        exit 1
    fi
    
    . /etc/os-release
    
    case $ID in
        ubuntu|debian)
            PACKAGE_MANAGER="apt"
            ;;
        fedora|centos|rhel)
            PACKAGE_MANAGER="dnf"
            ;;
        arch)
            PACKAGE_MANAGER="pacman"
            ;;
        *)
            print_warning "Unsupported OS: $ID. Installation may not work correctly."
            PACKAGE_MANAGER="apt"
            ;;
    esac
    
    print_status "Detected OS: $PRETTY_NAME"
    print_status "Package manager: $PACKAGE_MANAGER"
}

install_system_dependencies() {
    print_status "Installing system dependencies..."
    
    case $PACKAGE_MANAGER in
        apt)
            sudo apt update
            sudo apt install -y \
                python3 \
                python3-pip \
                python3-venv \
                git \
                build-essential \
                gcc-arm-none-eabi \
                stlink-tools \
                can-utils \
                python3-serial \
                python3-numpy \
                python3-matplotlib
            ;;
        dnf)
            sudo dnf install -y \
                python3 \
                python3-pip \
                git \
                gcc \
                make \
                arm-none-eabi-gcc-cs \
                arm-none-eabi-newlib \
                stlink \
                can-utils \
                python3-pyserial \
                python3-numpy \
                python3-matplotlib
            ;;
        pacman)
            sudo pacman -S --noconfirm \
                python \
                python-pip \
                git \
                base-devel \
                arm-none-eabi-gcc \
                arm-none-eabi-newlib \
                stlink \
                can-utils \
                python-pyserial \
                python-numpy \
                python-matplotlib
            ;;
    esac
    
    print_success "System dependencies installed"
}

create_aklippy_user() {
    if id "$AKLIPPY_USER" &>/dev/null; then
        print_status "User $AKLIPPY_USER already exists"
    else
        print_status "Creating user $AKLIPPY_USER..."
        sudo useradd -m -s /bin/bash $AKLIPPY_USER
        sudo usermod -a -G dialout,tty $AKLIPPY_USER
        print_success "User $AKLIPPY_USER created"
    fi
}

install_aklippy() {
    print_status "Installing AKlippy..."
    
    # Clone or update repository
    if [[ -d "$AKLIPPY_DIR" ]]; then
        print_status "Updating existing AKlippy installation..."
        cd "$AKLIPPY_DIR"
        sudo -u $AKLIPPY_USER git pull
    else
        print_status "Cloning AKlippy repository..."
        sudo -u $AKLIPPY_USER git clone https://github.com/your-org/aklippy.git "$AKLIPPY_DIR"
        cd "$AKLIPPY_DIR"
    fi
    
    # Create Python virtual environment
    print_status "Creating Python virtual environment..."
    sudo -u $AKLIPPY_USER python3 -m venv "$AKLIPPY_HOME/aklippy-env"
    
    # Install Python dependencies
    print_status "Installing Python dependencies..."
    sudo -u $AKLIPPY_USER "$AKLIPPY_HOME/aklippy-env/bin/pip" install -r requirements.txt
    
    # Make scripts executable
    sudo chmod +x "$AKLIPPY_DIR/aklippy_main.py"
    sudo chmod +x "$AKLIPPY_DIR/tools/build_firmware.py"
    sudo chmod +x "$AKLIPPY_DIR/tools/config_validator.py"
    
    print_success "AKlippy installed"
}

create_systemd_service() {
    print_status "Creating systemd service..."
    
    sudo tee /etc/systemd/system/$SERVICE_NAME.service > /dev/null <<EOF
[Unit]
Description=AKlippy Automotive ECU Framework
After=network.target
Wants=network.target

[Service]
Type=simple
User=$AKLIPPY_USER
Group=$AKLIPPY_USER
WorkingDirectory=$AKLIPPY_DIR
Environment=PATH=$AKLIPPY_HOME/aklippy-env/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
ExecStart=$AKLIPPY_HOME/aklippy-env/bin/python $AKLIPPY_DIR/aklippy_main.py $AKLIPPY_DIR/configs/example_basic.cfg
Restart=always
RestartSec=5

[Install]
WantedBy=multi-user.target
EOF

    sudo systemctl daemon-reload
    print_success "Systemd service created"
}

setup_udev_rules() {
    print_status "Setting up udev rules for hardware access..."
    
    sudo tee /etc/udev/rules.d/99-aklippy.rules > /dev/null <<EOF
# AKlippy udev rules
# ST-Link programmer
SUBSYSTEM=="usb", ATTR{idVendor}=="0483", ATTR{idProduct}=="3748", MODE="0666", GROUP="dialout"
SUBSYSTEM=="usb", ATTR{idVendor}=="0483", ATTR{idProduct}=="374b", MODE="0666", GROUP="dialout"

# CAN interfaces
KERNEL=="can*", MODE="0666", GROUP="dialout"

# Serial devices
KERNEL=="ttyUSB*", MODE="0666", GROUP="dialout"
KERNEL=="ttyACM*", MODE="0666", GROUP="dialout"
EOF

    sudo udevadm control --reload-rules
    sudo udevadm trigger
    
    print_success "Udev rules configured"
}

configure_can_interface() {
    print_status "Configuring CAN interface..."
    
    # Create CAN interface configuration
    sudo tee /etc/systemd/network/80-can.network > /dev/null <<EOF
[Match]
Name=can*

[CAN]
BitRate=500000
RestartSec=100ms
EOF

    # Enable CAN modules
    echo "can" | sudo tee -a /etc/modules > /dev/null
    echo "can_raw" | sudo tee -a /etc/modules > /dev/null
    echo "can_bcm" | sudo tee -a /etc/modules > /dev/null
    
    print_success "CAN interface configured"
}

create_config_directory() {
    print_status "Creating configuration directory..."
    
    CONFIG_DIR="$AKLIPPY_HOME/config"
    sudo -u $AKLIPPY_USER mkdir -p "$CONFIG_DIR"
    
    # Copy example configuration
    sudo -u $AKLIPPY_USER cp "$AKLIPPY_DIR/configs/example_basic.cfg" "$CONFIG_DIR/aklippy.cfg"
    
    print_success "Configuration directory created at $CONFIG_DIR"
}

show_completion_message() {
    print_success "AKlippy installation completed!"
    echo
    echo -e "${BLUE}Next steps:${NC}"
    echo "1. Edit the configuration file: $AKLIPPY_HOME/config/aklippy.cfg"
    echo "2. Validate the configuration: $AKLIPPY_DIR/tools/config_validator.py $AKLIPPY_HOME/config/aklippy.cfg"
    echo "3. Build firmware for your MCU: $AKLIPPY_DIR/tools/build_firmware.py build -t stm32f4 -c $AKLIPPY_HOME/config/aklippy.cfg"
    echo "4. Flash firmware to MCU: $AKLIPPY_DIR/tools/build_firmware.py flash -t stm32f4"
    echo "5. Start AKlippy service: sudo systemctl start $SERVICE_NAME"
    echo "6. Enable auto-start: sudo systemctl enable $SERVICE_NAME"
    echo
    echo -e "${BLUE}Useful commands:${NC}"
    echo "- Check service status: sudo systemctl status $SERVICE_NAME"
    echo "- View logs: sudo journalctl -u $SERVICE_NAME -f"
    echo "- Stop service: sudo systemctl stop $SERVICE_NAME"
    echo
    echo -e "${YELLOW}Important:${NC}"
    echo "- Make sure to configure your hardware pins in the config file"
    echo "- Test your configuration thoroughly before using on a real engine"
    echo "- Always follow safety procedures when working with automotive systems"
}

# Main installation process
main() {
    echo -e "${GREEN}AKlippy Installation Script${NC}"
    echo "=================================="
    echo
    
    check_root
    check_os
    
    print_status "Starting AKlippy installation..."
    
    install_system_dependencies
    create_aklippy_user
    install_aklippy
    create_systemd_service
    setup_udev_rules
    configure_can_interface
    create_config_directory
    
    show_completion_message
}

# Run main function
main "$@"
