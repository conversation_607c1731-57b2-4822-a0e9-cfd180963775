#!/bin/bash
# AKlippy Development Environment Setup Script

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [[ ! -f "aklippy_main.py" ]]; then
    print_error "Please run this script from the AKlippy root directory"
    exit 1
fi

print_status "Setting up AKlippy development environment..."

# Check Python version
python_version=$(python3 --version 2>&1 | cut -d' ' -f2 | cut -d'.' -f1,2)
required_version="3.8"

if [[ $(echo "$python_version >= $required_version" | bc -l) -eq 0 ]]; then
    print_error "Python $required_version or higher is required. Found: $python_version"
    exit 1
fi

print_success "Python version check passed: $python_version"

# Create virtual environment
if [[ ! -d "aklippy-env" ]]; then
    print_status "Creating Python virtual environment..."
    python3 -m venv aklippy-env
    print_success "Virtual environment created"
else
    print_status "Virtual environment already exists"
fi

# Activate virtual environment
print_status "Activating virtual environment..."
source aklippy-env/bin/activate

# Upgrade pip
print_status "Upgrading pip..."
pip install --upgrade pip

# Install development dependencies
print_status "Installing Python dependencies..."
pip install -r requirements.txt

# Install development tools
print_status "Installing development tools..."
pip install \
    pytest \
    pytest-cov \
    black \
    flake8 \
    mypy \
    pre-commit \
    sphinx \
    sphinx-rtd-theme

# Install pre-commit hooks
print_status "Setting up pre-commit hooks..."
pre-commit install

# Check for ARM GCC toolchain
print_status "Checking ARM GCC toolchain..."
if command -v arm-none-eabi-gcc &> /dev/null; then
    gcc_version=$(arm-none-eabi-gcc --version | head -n1)
    print_success "ARM GCC found: $gcc_version"
else
    print_warning "ARM GCC toolchain not found"
    print_warning "Install with: sudo apt install gcc-arm-none-eabi"
fi

# Check for st-flash
print_status "Checking flashing tools..."
if command -v st-flash &> /dev/null; then
    print_success "st-flash found"
else
    print_warning "st-flash not found"
    print_warning "Install with: sudo apt install stlink-tools"
fi

# Make scripts executable
print_status "Making scripts executable..."
chmod +x aklippy_main.py
chmod +x tools/*.py
chmod +x scripts/*.sh

# Create development directories
print_status "Creating development directories..."
mkdir -p logs
mkdir -p test_data
mkdir -p user_configs

# Run initial tests
print_status "Running initial tests..."
python -m pytest tests/ -v || print_warning "Some tests failed (this is normal for initial setup)"

# Validate example configuration
print_status "Validating example configuration..."
python tools/config_validator.py configs/example_basic.cfg

print_success "Development environment setup complete!"
echo
echo -e "${BLUE}Next steps:${NC}"
echo "1. Activate the virtual environment: source aklippy-env/bin/activate"
echo "2. Copy and edit a configuration: cp configs/example_basic.cfg my_config.cfg"
echo "3. Validate your config: python tools/config_validator.py my_config.cfg"
echo "4. Build firmware: python tools/build_firmware.py build -t stm32f4"
echo "5. Run AKlippy: python aklippy_main.py my_config.cfg"
echo
echo -e "${BLUE}Development commands:${NC}"
echo "- Run tests: pytest"
echo "- Format code: black ."
echo "- Lint code: flake8"
echo "- Type check: mypy aklippy/"
echo "- Build docs: cd docs && make html"
