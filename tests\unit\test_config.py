"""
Unit tests for configuration system
"""

import pytest
import tempfile
import os
from aklippy.config.configfile import ConfigFile, ConfigWrapper


class TestConfigFile:
    """Test configuration file parsing"""
    
    def test_basic_config_parsing(self):
        """Test basic configuration file parsing"""
        config_content = """
[mcu]
serial: /dev/ttyUSB0
baud: 250000

[engine]
cylinders: 4
displacement: 2000
"""
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.cfg', delete=False) as f:
            f.write(config_content)
            f.flush()
            
            try:
                # Mock printer object
                class MockPrinter:
                    pass
                
                printer = MockPrinter()
                config_file = ConfigFile(printer, f.name)
                config = config_file.read_main_config()
                
                # Test MCU section
                mcu_section = config.getsection('mcu')
                assert mcu_section.get('serial') == '/dev/ttyUSB0'
                assert mcu_section.getint('baud') == 250000
                
                # Test engine section
                engine_section = config.getsection('engine')
                assert engine_section.getint('cylinders') == 4
                assert engine_section.getint('displacement') == 2000
                
            finally:
                os.unlink(f.name)
                
    def test_config_validation(self):
        """Test configuration validation"""
        config_content = """
[mcu]
serial: /dev/ttyUSB0
baud: invalid_baud

[engine]
cylinders: 4
"""
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.cfg', delete=False) as f:
            f.write(config_content)
            f.flush()
            
            try:
                class MockPrinter:
                    pass
                
                printer = MockPrinter()
                config_file = ConfigFile(printer, f.name)
                config = config_file.read_main_config()
                
                mcu_section = config.getsection('mcu')
                
                # This should raise an exception for invalid integer
                with pytest.raises(Exception):
                    mcu_section.getint('baud')
                    
            finally:
                os.unlink(f.name)


class TestConfigWrapper:
    """Test configuration wrapper functionality"""
    
    def setup_method(self):
        """Setup test configuration"""
        self.config_data = {
            'test_section': {
                'string_option': 'test_value',
                'int_option': '42',
                'float_option': '3.14',
                'bool_option_true': 'true',
                'bool_option_false': 'false',
                'list_option': 'item1,item2,item3'
            }
        }
        
        class MockPrinter:
            pass
            
        self.printer = MockPrinter()
        
    def test_string_options(self):
        """Test string option parsing"""
        wrapper = ConfigWrapper(self.printer, self.config_data, 'test_section')
        
        assert wrapper.get('string_option') == 'test_value'
        assert wrapper.get('nonexistent', 'default') == 'default'
        
    def test_integer_options(self):
        """Test integer option parsing"""
        wrapper = ConfigWrapper(self.printer, self.config_data, 'test_section')
        
        assert wrapper.getint('int_option') == 42
        assert wrapper.getint('nonexistent', 100) == 100
        
        # Test range validation
        assert wrapper.getint('int_option', minval=0, maxval=100) == 42
        
        with pytest.raises(Exception):
            wrapper.getint('int_option', minval=50, maxval=100)
            
    def test_float_options(self):
        """Test float option parsing"""
        wrapper = ConfigWrapper(self.printer, self.config_data, 'test_section')
        
        assert wrapper.getfloat('float_option') == 3.14
        assert wrapper.getfloat('nonexistent', 2.71) == 2.71
        
    def test_boolean_options(self):
        """Test boolean option parsing"""
        wrapper = ConfigWrapper(self.printer, self.config_data, 'test_section')
        
        assert wrapper.getboolean('bool_option_true') is True
        assert wrapper.getboolean('bool_option_false') is False
        
    def test_list_options(self):
        """Test list option parsing"""
        wrapper = ConfigWrapper(self.printer, self.config_data, 'test_section')
        
        result = wrapper.getlist('list_option')
        assert result == ['item1', 'item2', 'item3']
        
        # Test integer list
        self.config_data['test_section']['int_list'] = '1,2,3,4'
        wrapper = ConfigWrapper(self.printer, self.config_data, 'test_section')
        int_result = wrapper.getintlist('int_list')
        assert int_result == [1, 2, 3, 4]
