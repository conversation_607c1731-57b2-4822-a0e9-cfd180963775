#!/usr/bin/env python3
"""
AKlippy Firmware Build Tool

This tool automates the building and flashing of AMCU firmware for different
target MCUs and configurations.
"""

import os
import sys
import subprocess
import argparse
import json
import logging
from pathlib import Path

class FirmwareBuildTool:
    """
    Firmware build and flash tool for AKlippy AMCU firmware.
    """
    
    def __init__(self):
        self.logger = logging.getLogger('build_tool')
        self.project_root = Path(__file__).parent.parent
        self.amcu_dir = self.project_root / 'amcu'
        self.configs_dir = self.project_root / 'configs'
        
        # Supported targets
        self.supported_targets = {
            'stm32f4': {
                'mcu': 'cortex-m4',
                'defines': ['STM32F4', 'STM32F407xx'],
                'linker_script': 'hal/stm32f4/stm32f407.ld',
                'flash_address': '0x8000000'
            },
            'stm32f7': {
                'mcu': 'cortex-m7', 
                'defines': ['STM32F7', 'STM32F767xx'],
                'linker_script': 'hal/stm32f7/stm32f767.ld',
                'flash_address': '0x8000000'
            }
        }
        
    def setup_logging(self, verbose=False):
        """Setup logging configuration"""
        level = logging.DEBUG if verbose else logging.INFO
        logging.basicConfig(
            level=level,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
    def check_dependencies(self):
        """Check if required build tools are available"""
        required_tools = [
            'arm-none-eabi-gcc',
            'arm-none-eabi-objcopy',
            'arm-none-eabi-size'
        ]
        
        missing_tools = []
        for tool in required_tools:
            try:
                subprocess.run([tool, '--version'], 
                             capture_output=True, check=True)
            except (subprocess.CalledProcessError, FileNotFoundError):
                missing_tools.append(tool)
                
        if missing_tools:
            self.logger.error("Missing required tools: %s", ', '.join(missing_tools))
            self.logger.error("Please install ARM GCC toolchain")
            return False
            
        return True
        
    def build_firmware(self, target, config_file=None, clean=False):
        """Build firmware for specified target"""
        if target not in self.supported_targets:
            self.logger.error("Unsupported target: %s", target)
            self.logger.error("Supported targets: %s", ', '.join(self.supported_targets.keys()))
            return False
            
        self.logger.info("Building firmware for target: %s", target)
        
        # Change to AMCU directory
        original_cwd = os.getcwd()
        os.chdir(self.amcu_dir)
        
        try:
            # Clean if requested
            if clean:
                self.logger.info("Cleaning previous build...")
                subprocess.run(['make', 'clean', f'TARGET={target}'], check=True)
                
            # Build firmware
            self.logger.info("Compiling firmware...")
            result = subprocess.run(['make', f'TARGET={target}'], 
                                  capture_output=True, text=True)
                                  
            if result.returncode != 0:
                self.logger.error("Build failed:")
                self.logger.error(result.stderr)
                return False
                
            self.logger.info("Build successful!")
            self.logger.info(result.stdout)
            
            # Show memory usage
            self.logger.info("Memory usage:")
            subprocess.run(['make', 'size', f'TARGET={target}'])
            
            return True
            
        except subprocess.CalledProcessError as e:
            self.logger.error("Build failed: %s", e)
            return False
        finally:
            os.chdir(original_cwd)
            
    def flash_firmware(self, target, method='st-flash'):
        """Flash firmware to MCU"""
        target_config = self.supported_targets.get(target)
        if not target_config:
            self.logger.error("Unsupported target: %s", target)
            return False
            
        firmware_path = self.amcu_dir / 'out' / f'amcu_{target}.bin'
        if not firmware_path.exists():
            self.logger.error("Firmware binary not found: %s", firmware_path)
            self.logger.error("Please build firmware first")
            return False
            
        self.logger.info("Flashing firmware to %s using %s", target, method)
        
        try:
            if method == 'st-flash':
                # Use st-flash utility
                cmd = ['st-flash', 'write', str(firmware_path), target_config['flash_address']]
                subprocess.run(cmd, check=True)
                
            elif method == 'openocd':
                # Use OpenOCD
                self.logger.error("OpenOCD flashing not implemented yet")
                return False
                
            elif method == 'dfu':
                # Use DFU utility
                cmd = ['dfu-util', '-a', '0', '-s', target_config['flash_address'], 
                       '-D', str(firmware_path)]
                subprocess.run(cmd, check=True)
                
            else:
                self.logger.error("Unsupported flash method: %s", method)
                return False
                
            self.logger.info("Firmware flashed successfully!")
            return True
            
        except subprocess.CalledProcessError as e:
            self.logger.error("Flash failed: %s", e)
            return False
        except FileNotFoundError:
            self.logger.error("Flash tool not found: %s", method)
            self.logger.error("Please install the required flashing tool")
            return False
            
    def generate_config_header(self, config_file, target):
        """Generate configuration header file from config"""
        if not config_file or not os.path.exists(config_file):
            self.logger.warning("Config file not found, using defaults")
            return True
            
        self.logger.info("Generating config header from: %s", config_file)
        
        # Parse configuration file
        import configparser
        config = configparser.ConfigParser()
        config.read(config_file)
        
        # Generate header file
        header_path = self.amcu_dir / 'src' / 'config_generated.h'
        
        with open(header_path, 'w') as f:
            f.write("/* Auto-generated configuration header */\n")
            f.write("#ifndef CONFIG_GENERATED_H\n")
            f.write("#define CONFIG_GENERATED_H\n\n")
            
            # Engine configuration
            if config.has_section('engine'):
                engine = config['engine']
                f.write("/* Engine Configuration */\n")
                f.write(f"#define ENGINE_CYLINDERS {engine.get('cylinders', '4')}\n")
                f.write(f"#define ENGINE_DISPLACEMENT {engine.get('displacement', '2000')}\n")
                f.write(f"#define ENGINE_REDLINE_RPM {engine.get('redline_rpm', '7000')}\n")
                f.write("\n")
                
            # Sensor configuration
            if config.has_section('sensors'):
                sensors = config['sensors']
                f.write("/* Sensor Configuration */\n")
                if sensors.get('coolant_temp_pin'):
                    f.write(f"#define COOLANT_TEMP_PIN \"{sensors.get('coolant_temp_pin')}\"\n")
                if sensors.get('map_sensor_pin'):
                    f.write(f"#define MAP_SENSOR_PIN \"{sensors.get('map_sensor_pin')}\"\n")
                f.write("\n")
                
            f.write("#endif /* CONFIG_GENERATED_H */\n")
            
        self.logger.info("Configuration header generated: %s", header_path)
        return True
        
    def list_targets(self):
        """List supported targets"""
        print("Supported targets:")
        for target, config in self.supported_targets.items():
            print(f"  {target:12} - {config['mcu']} ({', '.join(config['defines'])})")
            
    def list_configs(self):
        """List available configuration files"""
        print("Available configurations:")
        for config_file in self.configs_dir.glob('*.cfg'):
            print(f"  {config_file.name}")

def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description='AKlippy Firmware Build Tool')
    parser.add_argument('command', choices=['build', 'flash', 'clean', 'list-targets', 'list-configs'],
                       help='Command to execute')
    parser.add_argument('-t', '--target', default='stm32f4',
                       help='Target MCU (default: stm32f4)')
    parser.add_argument('-c', '--config', 
                       help='Configuration file path')
    parser.add_argument('-m', '--method', default='st-flash',
                       choices=['st-flash', 'openocd', 'dfu'],
                       help='Flash method (default: st-flash)')
    parser.add_argument('-v', '--verbose', action='store_true',
                       help='Enable verbose output')
    parser.add_argument('--clean', action='store_true',
                       help='Clean before building')
    
    args = parser.parse_args()
    
    # Create build tool instance
    build_tool = FirmwareBuildTool()
    build_tool.setup_logging(args.verbose)
    
    # Execute command
    if args.command == 'list-targets':
        build_tool.list_targets()
        return 0
        
    elif args.command == 'list-configs':
        build_tool.list_configs()
        return 0
        
    elif args.command == 'clean':
        # Change to AMCU directory and clean
        os.chdir(build_tool.amcu_dir)
        try:
            subprocess.run(['make', 'clean', f'TARGET={args.target}'], check=True)
            print(f"Cleaned build files for {args.target}")
            return 0
        except subprocess.CalledProcessError:
            print("Clean failed")
            return 1
            
    elif args.command == 'build':
        # Check dependencies
        if not build_tool.check_dependencies():
            return 1
            
        # Generate config header if config file provided
        if args.config:
            if not build_tool.generate_config_header(args.config, args.target):
                return 1
                
        # Build firmware
        if build_tool.build_firmware(args.target, args.config, args.clean):
            return 0
        else:
            return 1
            
    elif args.command == 'flash':
        # Flash firmware
        if build_tool.flash_firmware(args.target, args.method):
            return 0
        else:
            return 1
            
    return 0

if __name__ == '__main__':
    sys.exit(main())
