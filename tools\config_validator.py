#!/usr/bin/env python3
"""
AKlippy Configuration Validator

This tool validates AKlippy configuration files for correctness and completeness.
"""

import os
import sys
import argparse
import configparser
import logging
import re
from pathlib import Path

class ConfigValidator:
    """
    Configuration file validator for AKlippy.
    
    Validates configuration files for:
    - Required sections and options
    - Value ranges and types
    - Pin assignments and conflicts
    - Hardware compatibility
    """
    
    def __init__(self):
        self.logger = logging.getLogger('config_validator')
        self.errors = []
        self.warnings = []
        
        # Define required sections and their options
        self.required_sections = {
            'mcu': {
                'required': ['serial', 'baud'],
                'optional': ['mcu_type']
            },
            'engine': {
                'required': ['cylinders', 'displacement'],
                'optional': ['firing_order', 'compression_ratio', 'redline_rpm']
            }
        }
        
        # Define value constraints
        self.value_constraints = {
            'mcu': {
                'baud': {'type': int, 'min': 9600, 'max': 2000000},
                'mcu_type': {'type': str, 'choices': ['stm32f4', 'stm32f7']}
            },
            'engine': {
                'cylinders': {'type': int, 'min': 1, 'max': 12},
                'displacement': {'type': int, 'min': 50, 'max': 10000},
                'compression_ratio': {'type': float, 'min': 6.0, 'max': 20.0},
                'redline_rpm': {'type': int, 'min': 1000, 'max': 15000}
            },
            'fuel_system': {
                'injector_flow_rate': {'type': float, 'min': 50, 'max': 2000},
                'fuel_pressure': {'type': float, 'min': 1.0, 'max': 10.0},
                'dead_time': {'type': float, 'min': 0.1, 'max': 10.0}
            },
            'ignition_system': {
                'coil_charge_time': {'type': float, 'min': 1.0, 'max': 10.0},
                'dwell_time': {'type': float, 'min': 0.5, 'max': 10.0},
                'max_dwell_time': {'type': float, 'min': 1.0, 'max': 20.0}
            }
        }
        
        # Pin validation patterns
        self.pin_pattern = re.compile(r'^P[A-K]\d{1,2}$')
        self.used_pins = set()
        
    def validate_file(self, config_file):
        """Validate a configuration file"""
        self.errors = []
        self.warnings = []
        self.used_pins = set()
        
        if not os.path.exists(config_file):
            self.errors.append(f"Configuration file not found: {config_file}")
            return False
            
        try:
            config = configparser.ConfigParser()
            config.read(config_file)
        except Exception as e:
            self.errors.append(f"Failed to parse configuration file: {e}")
            return False
            
        self.logger.info(f"Validating configuration file: {config_file}")
        
        # Validate required sections
        self._validate_required_sections(config)
        
        # Validate section contents
        for section_name in config.sections():
            self._validate_section(config, section_name)
            
        # Validate pin assignments
        self._validate_pin_assignments(config)
        
        # Validate hardware compatibility
        self._validate_hardware_compatibility(config)
        
        # Report results
        self._report_results()
        
        return len(self.errors) == 0
        
    def _validate_required_sections(self, config):
        """Validate that required sections exist"""
        for section_name, section_def in self.required_sections.items():
            if not config.has_section(section_name):
                self.errors.append(f"Required section [{section_name}] is missing")
                continue
                
            section = config[section_name]
            
            # Check required options
            for option in section_def['required']:
                if option not in section:
                    self.errors.append(f"Required option '{option}' missing in section [{section_name}]")
                    
    def _validate_section(self, config, section_name):
        """Validate a specific section"""
        if section_name not in config:
            return
            
        section = config[section_name]
        constraints = self.value_constraints.get(section_name, {})
        
        for option, value in section.items():
            self._validate_option(section_name, option, value, constraints.get(option))
            
    def _validate_option(self, section_name, option, value, constraint):
        """Validate a specific option value"""
        if not constraint:
            return
            
        try:
            # Type validation
            if constraint['type'] == int:
                parsed_value = int(value)
            elif constraint['type'] == float:
                parsed_value = float(value)
            elif constraint['type'] == str:
                parsed_value = value
            else:
                return
                
            # Range validation
            if 'min' in constraint and parsed_value < constraint['min']:
                self.errors.append(f"Option '{option}' in [{section_name}] is below minimum: {parsed_value} < {constraint['min']}")
                
            if 'max' in constraint and parsed_value > constraint['max']:
                self.errors.append(f"Option '{option}' in [{section_name}] is above maximum: {parsed_value} > {constraint['max']}")
                
            # Choice validation
            if 'choices' in constraint and parsed_value not in constraint['choices']:
                self.errors.append(f"Option '{option}' in [{section_name}] has invalid value: {parsed_value}. Valid choices: {constraint['choices']}")
                
        except ValueError as e:
            self.errors.append(f"Option '{option}' in [{section_name}] has invalid type: {value} (expected {constraint['type'].__name__})")
            
    def _validate_pin_assignments(self, config):
        """Validate pin assignments for conflicts"""
        pin_assignments = {}
        
        # Collect all pin assignments
        for section_name in config.sections():
            section = config[section_name]
            
            for option, value in section.items():
                if 'pin' in option.lower():
                    pins = self._parse_pin_list(value)
                    for pin in pins:
                        if self._validate_pin_format(pin):
                            if pin in pin_assignments:
                                self.errors.append(f"Pin {pin} is assigned to multiple functions: {pin_assignments[pin]} and {section_name}.{option}")
                            else:
                                pin_assignments[pin] = f"{section_name}.{option}"
                                
    def _parse_pin_list(self, pin_string):
        """Parse a pin string that might contain multiple pins"""
        if ',' in pin_string:
            return [pin.strip() for pin in pin_string.split(',')]
        else:
            return [pin_string.strip()]
            
    def _validate_pin_format(self, pin):
        """Validate pin format (e.g., PA0, PB15)"""
        if not self.pin_pattern.match(pin):
            self.errors.append(f"Invalid pin format: {pin}. Expected format: P[A-K][0-15]")
            return False
        return True
        
    def _validate_hardware_compatibility(self, config):
        """Validate hardware compatibility"""
        # Check MCU capabilities
        if config.has_section('mcu'):
            mcu_type = config['mcu'].get('mcu_type', 'stm32f4')
            
            # Validate against MCU capabilities
            if mcu_type == 'stm32f4':
                self._validate_stm32f4_compatibility(config)
            elif mcu_type == 'stm32f7':
                self._validate_stm32f7_compatibility(config)
                
        # Check engine configuration consistency
        self._validate_engine_consistency(config)
        
    def _validate_stm32f4_compatibility(self, config):
        """Validate STM32F4 specific constraints"""
        # Check ADC channel limits
        adc_channels = 0
        for section_name in config.sections():
            section = config[section_name]
            for option, value in section.items():
                if 'sensor_pin' in option or 'temp_pin' in option:
                    adc_channels += 1
                    
        if adc_channels > 16:
            self.warnings.append(f"STM32F4 has limited ADC channels. Using {adc_channels} channels may require multiplexing")
            
        # Check timer limitations
        pwm_outputs = 0
        for section_name in config.sections():
            section = config[section_name]
            if 'pins' in section:
                pins = self._parse_pin_list(section['pins'])
                pwm_outputs += len(pins)
                
        if pwm_outputs > 12:
            self.warnings.append(f"STM32F4 has limited timer channels. {pwm_outputs} PWM outputs may exceed available timers")
            
    def _validate_stm32f7_compatibility(self, config):
        """Validate STM32F7 specific constraints"""
        # STM32F7 generally has more resources than F4
        pass
        
    def _validate_engine_consistency(self, config):
        """Validate engine configuration consistency"""
        if not config.has_section('engine'):
            return
            
        engine = config['engine']
        cylinders = int(engine.get('cylinders', 4))
        
        # Check fuel system consistency
        if config.has_section('fuel_system'):
            fuel = config['fuel_system']
            injector_count = int(fuel.get('injector_count', cylinders))
            
            if injector_count != cylinders:
                self.warnings.append(f"Injector count ({injector_count}) doesn't match cylinder count ({cylinders})")
                
            # Check injector pins
            if 'injector_pins' in fuel:
                pins = self._parse_pin_list(fuel['injector_pins'])
                if len(pins) != injector_count:
                    self.errors.append(f"Number of injector pins ({len(pins)}) doesn't match injector count ({injector_count})")
                    
        # Check ignition system consistency
        if config.has_section('ignition_system'):
            ignition = config['ignition_system']
            coil_count = int(ignition.get('coil_count', cylinders))
            
            if coil_count != cylinders:
                self.warnings.append(f"Coil count ({coil_count}) doesn't match cylinder count ({cylinders})")
                
            # Check coil pins
            if 'coil_pins' in ignition:
                pins = self._parse_pin_list(ignition['coil_pins'])
                if len(pins) != coil_count:
                    self.errors.append(f"Number of coil pins ({len(pins)}) doesn't match coil count ({coil_count})")
                    
    def _report_results(self):
        """Report validation results"""
        if self.errors:
            self.logger.error(f"Validation failed with {len(self.errors)} errors:")
            for error in self.errors:
                self.logger.error(f"  ERROR: {error}")
                
        if self.warnings:
            self.logger.warning(f"Validation completed with {len(self.warnings)} warnings:")
            for warning in self.warnings:
                self.logger.warning(f"  WARNING: {warning}")
                
        if not self.errors and not self.warnings:
            self.logger.info("Configuration validation passed with no issues")
        elif not self.errors:
            self.logger.info("Configuration validation passed with warnings")

def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description='AKlippy Configuration Validator')
    parser.add_argument('config_file', help='Configuration file to validate')
    parser.add_argument('-v', '--verbose', action='store_true',
                       help='Enable verbose output')
    parser.add_argument('--warnings-as-errors', action='store_true',
                       help='Treat warnings as errors')
    
    args = parser.parse_args()
    
    # Setup logging
    level = logging.DEBUG if args.verbose else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(levelname)s: %(message)s'
    )
    
    # Create validator and validate file
    validator = ConfigValidator()
    success = validator.validate_file(args.config_file)
    
    # Check if warnings should be treated as errors
    if args.warnings_as_errors and validator.warnings:
        success = False
        
    return 0 if success else 1

if __name__ == '__main__':
    sys.exit(main())
